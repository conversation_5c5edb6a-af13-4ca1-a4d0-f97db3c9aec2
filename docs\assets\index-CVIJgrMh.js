var Cr=Object.defineProperty;var Sr=(e,t,s)=>t in e?Cr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s;var J=(e,t,s)=>Sr(e,typeof t!="symbol"?t+"":t,s);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))n(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const a of o.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&n(a)}).observe(document,{childList:!0,subtree:!0});function s(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(i){if(i.ep)return;i.ep=!0;const o=s(i);fetch(i.href,o)}})();/**
* @vue/shared v3.5.8
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Vi(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const Pe={},Ps=[],It=()=>{},Tr=()=>!1,Hn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Hi=e=>e.startsWith("onUpdate:"),Ke=Object.assign,ji=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},wr=Object.prototype.hasOwnProperty,be=(e,t)=>wr.call(e,t),Z=Array.isArray,Is=e=>dn(e)==="[object Map]",ks=e=>dn(e)==="[object Set]",So=e=>dn(e)==="[object Date]",ie=e=>typeof e=="function",Ve=e=>typeof e=="string",Lt=e=>typeof e=="symbol",Le=e=>e!==null&&typeof e=="object",Oa=e=>(Le(e)||ie(e))&&ie(e.then)&&ie(e.catch),Ra=Object.prototype.toString,dn=e=>Ra.call(e),Er=e=>dn(e).slice(8,-1),Fa=e=>dn(e)==="[object Object]",Yi=e=>Ve(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Ks=Vi(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),jn=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},zr=/-(\w)/g,ps=jn(e=>e.replace(zr,(t,s)=>s?s.toUpperCase():"")),Pr=/\B([A-Z])/g,ms=jn(e=>e.replace(Pr,"-$1").toLowerCase()),ka=jn(e=>e.charAt(0).toUpperCase()+e.slice(1)),li=jn(e=>e?`on${ka(e)}`:""),Zt=(e,t)=>!Object.is(e,t),xn=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},Ua=(e,t,s,n=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:n,value:s})},On=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let To;const Wa=()=>To||(To=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Ls(e){if(Z(e)){const t={};for(let s=0;s<e.length;s++){const n=e[s],i=Ve(n)?Mr(n):Ls(n);if(i)for(const o in i)t[o]=i[o]}return t}else if(Ve(e)||Le(e))return e}const Ir=/;(?![^(]*\))/g,Lr=/:([^]+)/,xr=/\/\*[^]*?\*\//g;function Mr(e){const t={};return e.replace(xr,"").split(Ir).forEach(s=>{if(s){const n=s.split(Lr);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function Ue(e){let t="";if(Ve(e))t=e;else if(Z(e))for(let s=0;s<e.length;s++){const n=Ue(e[s]);n&&(t+=n+" ")}else if(Le(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const Ar="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Nr=Vi(Ar);function $a(e){return!!e||e===""}function Dr(e,t){if(e.length!==t.length)return!1;let s=!0;for(let n=0;s&&n<e.length;n++)s=gn(e[n],t[n]);return s}function gn(e,t){if(e===t)return!0;let s=So(e),n=So(t);if(s||n)return s&&n?e.getTime()===t.getTime():!1;if(s=Lt(e),n=Lt(t),s||n)return e===t;if(s=Z(e),n=Z(t),s||n)return s&&n?Dr(e,t):!1;if(s=Le(e),n=Le(t),s||n){if(!s||!n)return!1;const i=Object.keys(e).length,o=Object.keys(t).length;if(i!==o)return!1;for(const a in e){const l=e.hasOwnProperty(a),r=t.hasOwnProperty(a);if(l&&!r||!l&&r||!gn(e[a],t[a]))return!1}}return String(e)===String(t)}function Bi(e,t){return e.findIndex(s=>gn(s,t))}const Va=e=>!!(e&&e.__v_isRef===!0),A=e=>Ve(e)?e:e==null?"":Z(e)||Le(e)&&(e.toString===Ra||!ie(e.toString))?Va(e)?A(e.value):JSON.stringify(e,Ha,2):String(e),Ha=(e,t)=>Va(t)?Ha(e,t.value):Is(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[n,i],o)=>(s[ri(n,o)+" =>"]=i,s),{})}:ks(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>ri(s))}:Lt(t)?ri(t):Le(t)&&!Z(t)&&!Fa(t)?String(t):t,ri=(e,t="")=>{var s;return Lt(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.8
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let rt;class ja{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=rt,!t&&rt&&(this.index=(rt.scopes||(rt.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=rt;try{return rt=this,t()}finally{rt=s}}}on(){rt=this}off(){rt=this.parent}stop(t){if(this._active){let s,n;for(s=0,n=this.effects.length;s<n;s++)this.effects[s].stop();for(s=0,n=this.cleanups.length;s<n;s++)this.cleanups[s]();if(this.scopes)for(s=0,n=this.scopes.length;s<n;s++)this.scopes[s].stop(!0);if(!this.detached&&this.parent&&!t){const i=this.parent.scopes.pop();i&&i!==this&&(this.parent.scopes[this.index]=i,i.index=this.index)}this.parent=void 0,this._active=!1}}}function Or(e){return new ja(e)}function Rr(){return rt}let ze;const ci=new WeakSet;class Ya{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,rt&&rt.active&&rt.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,ci.has(this)&&(ci.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Xa(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,wo(this),Ga(this);const t=ze,s=Tt;ze=this,Tt=!0;try{return this.fn()}finally{Ka(this),ze=t,Tt=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Ki(t);this.deps=this.depsTail=void 0,wo(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?ci.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Ci(this)&&this.run()}get dirty(){return Ci(this)}}let Ba=0,Js;function Xa(e){e.flags|=8,e.next=Js,Js=e}function Xi(){Ba++}function Gi(){if(--Ba>0)return;let e;for(;Js;){let t=Js;for(Js=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(n){e||(e=n)}t=s}}if(e)throw e}function Ga(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Ka(e,t=!1){let s,n=e.depsTail,i=n;for(;i;){const o=i.prevDep;i.version===-1?(i===n&&(n=o),Ki(i,t),Fr(i)):s=i,i.dep.activeLink=i.prevActiveLink,i.prevActiveLink=void 0,i=o}e.deps=s,e.depsTail=n}function Ci(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Ja(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Ja(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===tn))return;e.globalVersion=tn;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!Ci(e)){e.flags&=-3;return}const s=ze,n=Tt;ze=e,Tt=!0;try{Ga(e);const i=e.fn(e._value);(t.version===0||Zt(i,e._value))&&(e._value=i,t.version++)}catch(i){throw t.version++,i}finally{ze=s,Tt=n,Ka(e,!0),e.flags&=-3}}function Ki(e,t=!1){const{dep:s,prevSub:n,nextSub:i}=e;if(n&&(n.nextSub=i,e.prevSub=void 0),i&&(i.prevSub=n,e.nextSub=void 0),s.subs===e&&(s.subs=n),!s.subs)if(s.computed){s.computed.flags&=-5;for(let o=s.computed.deps;o;o=o.nextDep)Ki(o,!0)}else s.map&&!t&&(s.map.delete(s.key),s.map.size||Rn.delete(s.target))}function Fr(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let Tt=!0;const Qa=[];function es(){Qa.push(Tt),Tt=!1}function ts(){const e=Qa.pop();Tt=e===void 0?!0:e}function wo(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=ze;ze=void 0;try{t()}finally{ze=s}}}let tn=0;class kr{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Ji{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.target=void 0,this.map=void 0,this.key=void 0}track(t){if(!ze||!Tt||ze===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==ze)s=this.activeLink=new kr(ze,this),ze.deps?(s.prevDep=ze.depsTail,ze.depsTail.nextDep=s,ze.depsTail=s):ze.deps=ze.depsTail=s,ze.flags&4&&qa(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const n=s.nextDep;n.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=n),s.prevDep=ze.depsTail,s.nextDep=void 0,ze.depsTail.nextDep=s,ze.depsTail=s,ze.deps===s&&(ze.deps=n)}return s}trigger(t){this.version++,tn++,this.notify(t)}notify(t){Xi();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{Gi()}}}function qa(e){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let n=t.deps;n;n=n.nextDep)qa(n)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),e.dep.subs=e}const Rn=new WeakMap,us=Symbol(""),Si=Symbol(""),sn=Symbol("");function qe(e,t,s){if(Tt&&ze){let n=Rn.get(e);n||Rn.set(e,n=new Map);let i=n.get(s);i||(n.set(s,i=new Ji),i.target=e,i.map=n,i.key=s),i.track()}}function Ht(e,t,s,n,i,o){const a=Rn.get(e);if(!a){tn++;return}const l=r=>{r&&r.trigger()};if(Xi(),t==="clear")a.forEach(l);else{const r=Z(e),u=r&&Yi(s);if(r&&s==="length"){const f=Number(n);a.forEach((d,h)=>{(h==="length"||h===sn||!Lt(h)&&h>=f)&&l(d)})}else switch(s!==void 0&&l(a.get(s)),u&&l(a.get(sn)),t){case"add":r?u&&l(a.get("length")):(l(a.get(us)),Is(e)&&l(a.get(Si)));break;case"delete":r||(l(a.get(us)),Is(e)&&l(a.get(Si)));break;case"set":Is(e)&&l(a.get(us));break}}Gi()}function Cs(e){const t=Ce(e);return t===e?t:(qe(t,"iterate",sn),yt(e)?t:t.map(Je))}function Yn(e){return qe(e=Ce(e),"iterate",sn),e}const Ur={__proto__:null,[Symbol.iterator](){return fi(this,Symbol.iterator,Je)},concat(...e){return Cs(this).concat(...e.map(t=>Z(t)?Cs(t):t))},entries(){return fi(this,"entries",e=>(e[1]=Je(e[1]),e))},every(e,t){return Dt(this,"every",e,t,void 0,arguments)},filter(e,t){return Dt(this,"filter",e,t,s=>s.map(Je),arguments)},find(e,t){return Dt(this,"find",e,t,Je,arguments)},findIndex(e,t){return Dt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Dt(this,"findLast",e,t,Je,arguments)},findLastIndex(e,t){return Dt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Dt(this,"forEach",e,t,void 0,arguments)},includes(...e){return ui(this,"includes",e)},indexOf(...e){return ui(this,"indexOf",e)},join(e){return Cs(this).join(e)},lastIndexOf(...e){return ui(this,"lastIndexOf",e)},map(e,t){return Dt(this,"map",e,t,void 0,arguments)},pop(){return js(this,"pop")},push(...e){return js(this,"push",e)},reduce(e,...t){return Eo(this,"reduce",e,t)},reduceRight(e,...t){return Eo(this,"reduceRight",e,t)},shift(){return js(this,"shift")},some(e,t){return Dt(this,"some",e,t,void 0,arguments)},splice(...e){return js(this,"splice",e)},toReversed(){return Cs(this).toReversed()},toSorted(e){return Cs(this).toSorted(e)},toSpliced(...e){return Cs(this).toSpliced(...e)},unshift(...e){return js(this,"unshift",e)},values(){return fi(this,"values",Je)}};function fi(e,t,s){const n=Yn(e),i=n[t]();return n!==e&&!yt(e)&&(i._next=i.next,i.next=()=>{const o=i._next();return o.value&&(o.value=s(o.value)),o}),i}const Wr=Array.prototype;function Dt(e,t,s,n,i,o){const a=Yn(e),l=a!==e&&!yt(e),r=a[t];if(r!==Wr[t]){const d=r.apply(e,o);return l?Je(d):d}let u=s;a!==e&&(l?u=function(d,h){return s.call(this,Je(d),h,e)}:s.length>2&&(u=function(d,h){return s.call(this,d,h,e)}));const f=r.call(a,u,n);return l&&i?i(f):f}function Eo(e,t,s,n){const i=Yn(e);let o=s;return i!==e&&(yt(e)?s.length>3&&(o=function(a,l,r){return s.call(this,a,l,r,e)}):o=function(a,l,r){return s.call(this,a,Je(l),r,e)}),i[t](o,...n)}function ui(e,t,s){const n=Ce(e);qe(n,"iterate",sn);const i=n[t](...s);return(i===-1||i===!1)&&to(s[0])?(s[0]=Ce(s[0]),n[t](...s)):i}function js(e,t,s=[]){es(),Xi();const n=Ce(e)[t].apply(e,s);return Gi(),ts(),n}const $r=Vi("__proto__,__v_isRef,__isVue"),Za=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Lt));function Vr(e){Lt(e)||(e=String(e));const t=Ce(this);return qe(t,"has",e),t.hasOwnProperty(e)}class el{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,n){const i=this._isReadonly,o=this._isShallow;if(s==="__v_isReactive")return!i;if(s==="__v_isReadonly")return i;if(s==="__v_isShallow")return o;if(s==="__v_raw")return n===(i?o?tc:il:o?nl:sl).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(n)?t:void 0;const a=Z(t);if(!i){let r;if(a&&(r=Ur[s]))return r;if(s==="hasOwnProperty")return Vr}const l=Reflect.get(t,s,Ge(t)?t:n);return(Lt(s)?Za.has(s):$r(s))||(i||qe(t,"get",s),o)?l:Ge(l)?a&&Yi(s)?l:l.value:Le(l)?i?ol(l):Zi(l):l}}class tl extends el{constructor(t=!1){super(!1,t)}set(t,s,n,i){let o=t[s];if(!this._isShallow){const r=ds(o);if(!yt(n)&&!ds(n)&&(o=Ce(o),n=Ce(n)),!Z(t)&&Ge(o)&&!Ge(n))return r?!1:(o.value=n,!0)}const a=Z(t)&&Yi(s)?Number(s)<t.length:be(t,s),l=Reflect.set(t,s,n,Ge(t)?t:i);return t===Ce(i)&&(a?Zt(n,o)&&Ht(t,"set",s,n):Ht(t,"add",s,n)),l}deleteProperty(t,s){const n=be(t,s);t[s];const i=Reflect.deleteProperty(t,s);return i&&n&&Ht(t,"delete",s,void 0),i}has(t,s){const n=Reflect.has(t,s);return(!Lt(s)||!Za.has(s))&&qe(t,"has",s),n}ownKeys(t){return qe(t,"iterate",Z(t)?"length":us),Reflect.ownKeys(t)}}class Hr extends el{constructor(t=!1){super(!0,t)}set(t,s){return!0}deleteProperty(t,s){return!0}}const jr=new tl,Yr=new Hr,Br=new tl(!0);const Qi=e=>e,Bn=e=>Reflect.getPrototypeOf(e);function bn(e,t,s=!1,n=!1){e=e.__v_raw;const i=Ce(e),o=Ce(t);s||(Zt(t,o)&&qe(i,"get",t),qe(i,"get",o));const{has:a}=Bn(i),l=n?Qi:s?so:Je;if(a.call(i,t))return l(e.get(t));if(a.call(i,o))return l(e.get(o));e!==i&&e.get(t)}function Cn(e,t=!1){const s=this.__v_raw,n=Ce(s),i=Ce(e);return t||(Zt(e,i)&&qe(n,"has",e),qe(n,"has",i)),e===i?s.has(e):s.has(e)||s.has(i)}function Sn(e,t=!1){return e=e.__v_raw,!t&&qe(Ce(e),"iterate",us),Reflect.get(e,"size",e)}function zo(e,t=!1){!t&&!yt(e)&&!ds(e)&&(e=Ce(e));const s=Ce(this);return Bn(s).has.call(s,e)||(s.add(e),Ht(s,"add",e,e)),this}function Po(e,t,s=!1){!s&&!yt(t)&&!ds(t)&&(t=Ce(t));const n=Ce(this),{has:i,get:o}=Bn(n);let a=i.call(n,e);a||(e=Ce(e),a=i.call(n,e));const l=o.call(n,e);return n.set(e,t),a?Zt(t,l)&&Ht(n,"set",e,t):Ht(n,"add",e,t),this}function Io(e){const t=Ce(this),{has:s,get:n}=Bn(t);let i=s.call(t,e);i||(e=Ce(e),i=s.call(t,e)),n&&n.call(t,e);const o=t.delete(e);return i&&Ht(t,"delete",e,void 0),o}function Lo(){const e=Ce(this),t=e.size!==0,s=e.clear();return t&&Ht(e,"clear",void 0,void 0),s}function Tn(e,t){return function(n,i){const o=this,a=o.__v_raw,l=Ce(a),r=t?Qi:e?so:Je;return!e&&qe(l,"iterate",us),a.forEach((u,f)=>n.call(i,r(u),r(f),o))}}function wn(e,t,s){return function(...n){const i=this.__v_raw,o=Ce(i),a=Is(o),l=e==="entries"||e===Symbol.iterator&&a,r=e==="keys"&&a,u=i[e](...n),f=s?Qi:t?so:Je;return!t&&qe(o,"iterate",r?Si:us),{next(){const{value:d,done:h}=u.next();return h?{value:d,done:h}:{value:l?[f(d[0]),f(d[1])]:f(d),done:h}},[Symbol.iterator](){return this}}}}function Xt(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Xr(){const e={get(o){return bn(this,o)},get size(){return Sn(this)},has:Cn,add:zo,set:Po,delete:Io,clear:Lo,forEach:Tn(!1,!1)},t={get(o){return bn(this,o,!1,!0)},get size(){return Sn(this)},has:Cn,add(o){return zo.call(this,o,!0)},set(o,a){return Po.call(this,o,a,!0)},delete:Io,clear:Lo,forEach:Tn(!1,!0)},s={get(o){return bn(this,o,!0)},get size(){return Sn(this,!0)},has(o){return Cn.call(this,o,!0)},add:Xt("add"),set:Xt("set"),delete:Xt("delete"),clear:Xt("clear"),forEach:Tn(!0,!1)},n={get(o){return bn(this,o,!0,!0)},get size(){return Sn(this,!0)},has(o){return Cn.call(this,o,!0)},add:Xt("add"),set:Xt("set"),delete:Xt("delete"),clear:Xt("clear"),forEach:Tn(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(o=>{e[o]=wn(o,!1,!1),s[o]=wn(o,!0,!1),t[o]=wn(o,!1,!0),n[o]=wn(o,!0,!0)}),[e,s,t,n]}const[Gr,Kr,Jr,Qr]=Xr();function qi(e,t){const s=t?e?Qr:Jr:e?Kr:Gr;return(n,i,o)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?n:Reflect.get(be(s,i)&&i in n?s:n,i,o)}const qr={get:qi(!1,!1)},Zr={get:qi(!1,!0)},ec={get:qi(!0,!1)};const sl=new WeakMap,nl=new WeakMap,il=new WeakMap,tc=new WeakMap;function sc(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function nc(e){return e.__v_skip||!Object.isExtensible(e)?0:sc(Er(e))}function Zi(e){return ds(e)?e:eo(e,!1,jr,qr,sl)}function ic(e){return eo(e,!1,Br,Zr,nl)}function ol(e){return eo(e,!0,Yr,ec,il)}function eo(e,t,s,n,i){if(!Le(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=i.get(e);if(o)return o;const a=nc(e);if(a===0)return e;const l=new Proxy(e,a===2?n:s);return i.set(e,l),l}function xs(e){return ds(e)?xs(e.__v_raw):!!(e&&e.__v_isReactive)}function ds(e){return!!(e&&e.__v_isReadonly)}function yt(e){return!!(e&&e.__v_isShallow)}function to(e){return e?!!e.__v_raw:!1}function Ce(e){const t=e&&e.__v_raw;return t?Ce(t):e}function oc(e){return!be(e,"__v_skip")&&Object.isExtensible(e)&&Ua(e,"__v_skip",!0),e}const Je=e=>Le(e)?Zi(e):e,so=e=>Le(e)?ol(e):e;function Ge(e){return e?e.__v_isRef===!0:!1}function V(e){return al(e,!1)}function ac(e){return al(e,!0)}function al(e,t){return Ge(e)?e:new lc(e,t)}class lc{constructor(t,s){this.dep=new Ji,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:Ce(t),this._value=s?t:Je(t),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(t){const s=this._rawValue,n=this.__v_isShallow||yt(t)||ds(t);t=n?t:Ce(t),Zt(t,s)&&(this._rawValue=t,this._value=n?t:Je(t),this.dep.trigger())}}function F(e){return Ge(e)?e.value:e}const rc={get:(e,t,s)=>t==="__v_raw"?e:F(Reflect.get(e,t,s)),set:(e,t,s,n)=>{const i=e[t];return Ge(i)&&!Ge(s)?(i.value=s,!0):Reflect.set(e,t,s,n)}};function ll(e){return xs(e)?e:new Proxy(e,rc)}class cc{constructor(t,s,n){this.fn=t,this.setter=s,this._value=void 0,this.dep=new Ji(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=tn-1,this.effect=this,this.__v_isReadonly=!s,this.isSSR=n}notify(){if(this.flags|=16,!(this.flags&8)&&ze!==this)return Xa(this),!0}get value(){const t=this.dep.track();return Ja(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function fc(e,t,s=!1){let n,i;return ie(e)?n=e:(n=e.get,i=e.set),new cc(n,i,s)}const En={},Fn=new WeakMap;let cs;function uc(e,t=!1,s=cs){if(s){let n=Fn.get(s);n||Fn.set(s,n=[]),n.push(e)}}function pc(e,t,s=Pe){const{immediate:n,deep:i,once:o,scheduler:a,augmentJob:l,call:r}=s,u=b=>i?b:yt(b)||i===!1||i===0?Wt(b,1):Wt(b);let f,d,h,T,L=!1,M=!1;if(Ge(e)?(d=()=>e.value,L=yt(e)):xs(e)?(d=()=>u(e),L=!0):Z(e)?(M=!0,L=e.some(b=>xs(b)||yt(b)),d=()=>e.map(b=>{if(Ge(b))return b.value;if(xs(b))return u(b);if(ie(b))return r?r(b,2):b()})):ie(e)?t?d=r?()=>r(e,2):e:d=()=>{if(h){es();try{h()}finally{ts()}}const b=cs;cs=f;try{return r?r(e,3,[T]):e(T)}finally{cs=b}}:d=It,t&&i){const b=d,D=i===!0?1/0:i;d=()=>Wt(b(),D)}const U=Rr(),y=()=>{f.stop(),U&&ji(U.effects,f)};if(o&&t){const b=t;t=(...D)=>{b(...D),y()}}let w=M?new Array(e.length).fill(En):En;const x=b=>{if(!(!(f.flags&1)||!f.dirty&&!b))if(t){const D=f.run();if(i||L||(M?D.some(($,R)=>Zt($,w[R])):Zt(D,w))){h&&h();const $=cs;cs=f;try{const R=[D,w===En?void 0:M&&w[0]===En?[]:w,T];r?r(t,3,R):t(...R),w=D}finally{cs=$}}}else f.run()};return l&&l(x),f=new Ya(d),f.scheduler=a?()=>a(x,!1):x,T=b=>uc(b,!1,f),h=f.onStop=()=>{const b=Fn.get(f);if(b){if(r)r(b,4);else for(const D of b)D();Fn.delete(f)}},t?n?x(!0):w=f.run():a?a(x.bind(null,!0),!0):f.run(),y.pause=f.pause.bind(f),y.resume=f.resume.bind(f),y.stop=y,y}function Wt(e,t=1/0,s){if(t<=0||!Le(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,Ge(e))Wt(e.value,t,s);else if(Z(e))for(let n=0;n<e.length;n++)Wt(e[n],t,s);else if(ks(e)||Is(e))e.forEach(n=>{Wt(n,t,s)});else if(Fa(e)){for(const n in e)Wt(e[n],t,s);for(const n of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,n)&&Wt(e[n],t,s)}return e}/**
* @vue/runtime-core v3.5.8
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function mn(e,t,s,n){try{return n?e(...n):e()}catch(i){Xn(i,t,s)}}function xt(e,t,s,n){if(ie(e)){const i=mn(e,t,s,n);return i&&Oa(i)&&i.catch(o=>{Xn(o,t,s)}),i}if(Z(e)){const i=[];for(let o=0;o<e.length;o++)i.push(xt(e[o],t,s,n));return i}}function Xn(e,t,s,n=!0){const i=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:a}=t&&t.appContext.config||Pe;if(t){let l=t.parent;const r=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${s}`;for(;l;){const f=l.ec;if(f){for(let d=0;d<f.length;d++)if(f[d](e,r,u)===!1)return}l=l.parent}if(o){es(),mn(o,null,10,[e,r,u]),ts();return}}dc(e,s,i,n,a)}function dc(e,t,s,n=!0,i=!1){if(i)throw e;console.error(e)}let nn=!1,Ti=!1;const it=[];let zt=0;const Ms=[];let Jt=null,Ts=0;const rl=Promise.resolve();let no=null;function zs(e){const t=no||rl;return e?t.then(this?e.bind(this):e):t}function gc(e){let t=nn?zt+1:0,s=it.length;for(;t<s;){const n=t+s>>>1,i=it[n],o=on(i);o<e||o===e&&i.flags&2?t=n+1:s=n}return t}function io(e){if(!(e.flags&1)){const t=on(e),s=it[it.length-1];!s||!(e.flags&2)&&t>=on(s)?it.push(e):it.splice(gc(t),0,e),e.flags|=1,cl()}}function cl(){!nn&&!Ti&&(Ti=!0,no=rl.then(ul))}function mc(e){Z(e)?Ms.push(...e):Jt&&e.id===-1?Jt.splice(Ts+1,0,e):e.flags&1||(Ms.push(e),e.flags|=1),cl()}function xo(e,t,s=nn?zt+1:0){for(;s<it.length;s++){const n=it[s];if(n&&n.flags&2){if(e&&n.id!==e.uid)continue;it.splice(s,1),s--,n.flags&4&&(n.flags&=-2),n(),n.flags&4||(n.flags&=-2)}}}function fl(e){if(Ms.length){const t=[...new Set(Ms)].sort((s,n)=>on(s)-on(n));if(Ms.length=0,Jt){Jt.push(...t);return}for(Jt=t,Ts=0;Ts<Jt.length;Ts++){const s=Jt[Ts];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}Jt=null,Ts=0}}const on=e=>e.id==null?e.flags&2?-1:1/0:e.id;function ul(e){Ti=!1,nn=!0;try{for(zt=0;zt<it.length;zt++){const t=it[zt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),mn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;zt<it.length;zt++){const t=it[zt];t&&(t.flags&=-2)}zt=0,it.length=0,fl(),nn=!1,no=null,(it.length||Ms.length)&&ul()}}let ft=null,pl=null;function kn(e){const t=ft;return ft=e,pl=e&&e.type.__scopeId||null,t}function hc(e,t=ft,s){if(!t||e._n)return e;const n=(...i)=>{n._d&&ko(-1);const o=kn(t);let a;try{a=e(...i)}finally{kn(o),n._d&&ko(1)}return a};return n._n=!0,n._c=!0,n._d=!0,n}function B(e,t){if(ft===null)return e;const s=Zn(ft),n=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[o,a,l,r=Pe]=t[i];o&&(ie(o)&&(o={mounted:o,updated:o}),o.deep&&Wt(a),n.push({dir:o,instance:s,value:a,oldValue:void 0,arg:l,modifiers:r}))}return e}function ls(e,t,s,n){const i=e.dirs,o=t&&t.dirs;for(let a=0;a<i.length;a++){const l=i[a];o&&(l.oldValue=o[a].value);let r=l.dir[n];r&&(es(),xt(r,s,8,[e.el,l,e,t]),ts())}}const yc=Symbol("_vte"),vc=e=>e.__isTeleport;function oo(e,t){e.shapeFlag&6&&e.component?(e.transition=t,oo(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function hs(e,t){return ie(e)?Ke({name:e.name},t,{setup:e}):e}function dl(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function wi(e,t,s,n,i=!1){if(Z(e)){e.forEach((L,M)=>wi(L,t&&(Z(t)?t[M]:t),s,n,i));return}if(Qs(n)&&!i)return;const o=n.shapeFlag&4?Zn(n.component):n.el,a=i?null:o,{i:l,r}=e,u=t&&t.r,f=l.refs===Pe?l.refs={}:l.refs,d=l.setupState,h=Ce(d),T=d===Pe?()=>!1:L=>be(h,L);if(u!=null&&u!==r&&(Ve(u)?(f[u]=null,T(u)&&(d[u]=null)):Ge(u)&&(u.value=null)),ie(r))mn(r,l,12,[a,f]);else{const L=Ve(r),M=Ge(r);if(L||M){const U=()=>{if(e.f){const y=L?T(r)?d[r]:f[r]:r.value;i?Z(y)&&ji(y,o):Z(y)?y.includes(o)||y.push(o):L?(f[r]=[o],T(r)&&(d[r]=f[r])):(r.value=[o],e.k&&(f[e.k]=r.value))}else L?(f[r]=a,T(r)&&(d[r]=a)):M&&(r.value=a,e.k&&(f[e.k]=a))};a?(U.id=-1,lt(U,s)):U()}}}const Qs=e=>!!e.type.__asyncLoader,gl=e=>e.type.__isKeepAlive;function _c(e,t){ml(e,"a",t)}function bc(e,t){ml(e,"da",t)}function ml(e,t,s=Qe){const n=e.__wdc||(e.__wdc=()=>{let i=s;for(;i;){if(i.isDeactivated)return;i=i.parent}return e()});if(Gn(t,n,s),s){let i=s.parent;for(;i&&i.parent;)gl(i.parent.vnode)&&Cc(n,t,s,i),i=i.parent}}function Cc(e,t,s,n){const i=Gn(t,e,n,!0);Jn(()=>{ji(n[t],i)},s)}function Gn(e,t,s=Qe,n=!1){if(s){const i=s[e]||(s[e]=[]),o=t.__weh||(t.__weh=(...a)=>{es();const l=yn(s),r=xt(t,s,e,a);return l(),ts(),r});return n?i.unshift(o):i.push(o),o}}const Yt=e=>(t,s=Qe)=>{(!qn||e==="sp")&&Gn(e,(...n)=>t(...n),s)},Sc=Yt("bm"),Kn=Yt("m"),Tc=Yt("bu"),wc=Yt("u"),Ec=Yt("bum"),Jn=Yt("um"),zc=Yt("sp"),Pc=Yt("rtg"),Ic=Yt("rtc");function Lc(e,t=Qe){Gn("ec",e,t)}const xc=Symbol.for("v-ndc");function Ft(e,t,s,n){let i;const o=s,a=Z(e);if(a||Ve(e)){const l=a&&xs(e);let r=!1;l&&(r=!yt(e),e=Yn(e)),i=new Array(e.length);for(let u=0,f=e.length;u<f;u++)i[u]=t(r?Je(e[u]):e[u],u,void 0,o)}else if(typeof e=="number"){i=new Array(e);for(let l=0;l<e;l++)i[l]=t(l+1,l,void 0,o)}else if(Le(e))if(e[Symbol.iterator])i=Array.from(e,(l,r)=>t(l,r,void 0,o));else{const l=Object.keys(e);i=new Array(l.length);for(let r=0,u=l.length;r<u;r++){const f=l[r];i[r]=t(e[f],f,r,o)}}else i=[];return i}const Ei=e=>e?Rl(e)?Zn(e):Ei(e.parent):null,qs=Ke(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ei(e.parent),$root:e=>Ei(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>ao(e),$forceUpdate:e=>e.f||(e.f=()=>{io(e.update)}),$nextTick:e=>e.n||(e.n=zs.bind(e.proxy)),$watch:e=>qc.bind(e)}),pi=(e,t)=>e!==Pe&&!e.__isScriptSetup&&be(e,t),Mc={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:n,data:i,props:o,accessCache:a,type:l,appContext:r}=e;let u;if(t[0]!=="$"){const T=a[t];if(T!==void 0)switch(T){case 1:return n[t];case 2:return i[t];case 4:return s[t];case 3:return o[t]}else{if(pi(n,t))return a[t]=1,n[t];if(i!==Pe&&be(i,t))return a[t]=2,i[t];if((u=e.propsOptions[0])&&be(u,t))return a[t]=3,o[t];if(s!==Pe&&be(s,t))return a[t]=4,s[t];zi&&(a[t]=0)}}const f=qs[t];let d,h;if(f)return t==="$attrs"&&qe(e.attrs,"get",""),f(e);if((d=l.__cssModules)&&(d=d[t]))return d;if(s!==Pe&&be(s,t))return a[t]=4,s[t];if(h=r.config.globalProperties,be(h,t))return h[t]},set({_:e},t,s){const{data:n,setupState:i,ctx:o}=e;return pi(i,t)?(i[t]=s,!0):n!==Pe&&be(n,t)?(n[t]=s,!0):be(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:n,appContext:i,propsOptions:o}},a){let l;return!!s[a]||e!==Pe&&be(e,a)||pi(t,a)||(l=o[0])&&be(l,a)||be(n,a)||be(qs,a)||be(i.config.globalProperties,a)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:be(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};function Mo(e){return Z(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}let zi=!0;function Ac(e){const t=ao(e),s=e.proxy,n=e.ctx;zi=!1,t.beforeCreate&&Ao(t.beforeCreate,e,"bc");const{data:i,computed:o,methods:a,watch:l,provide:r,inject:u,created:f,beforeMount:d,mounted:h,beforeUpdate:T,updated:L,activated:M,deactivated:U,beforeDestroy:y,beforeUnmount:w,destroyed:x,unmounted:b,render:D,renderTracked:$,renderTriggered:R,errorCaptured:se,serverPrefetch:fe,expose:ve,inheritAttrs:_e,components:ce,directives:je,filters:Ze}=t;if(u&&Nc(u,n,null),a)for(const ge in a){const re=a[ge];ie(re)&&(n[ge]=re.bind(s))}if(i){const ge=i.call(s,s);Le(ge)&&(e.data=Zi(ge))}if(zi=!0,o)for(const ge in o){const re=o[ge],Me=ie(re)?re.bind(s,s):ie(re.get)?re.get.bind(s,s):It,et=!ie(re)&&ie(re.set)?re.set.bind(s):It,Ye=ws({get:Me,set:et});Object.defineProperty(n,ge,{enumerable:!0,configurable:!0,get:()=>Ye.value,set:q=>Ye.value=q})}if(l)for(const ge in l)hl(l[ge],n,s,ge);if(r){const ge=ie(r)?r.call(s):r;Reflect.ownKeys(ge).forEach(re=>{Uc(re,ge[re])})}f&&Ao(f,e,"c");function xe(ge,re){Z(re)?re.forEach(Me=>ge(Me.bind(s))):re&&ge(re.bind(s))}if(xe(Sc,d),xe(Kn,h),xe(Tc,T),xe(wc,L),xe(_c,M),xe(bc,U),xe(Lc,se),xe(Ic,$),xe(Pc,R),xe(Ec,w),xe(Jn,b),xe(zc,fe),Z(ve))if(ve.length){const ge=e.exposed||(e.exposed={});ve.forEach(re=>{Object.defineProperty(ge,re,{get:()=>s[re],set:Me=>s[re]=Me})})}else e.exposed||(e.exposed={});D&&e.render===It&&(e.render=D),_e!=null&&(e.inheritAttrs=_e),ce&&(e.components=ce),je&&(e.directives=je),fe&&dl(e)}function Nc(e,t,s=It){Z(e)&&(e=Pi(e));for(const n in e){const i=e[n];let o;Le(i)?"default"in i?o=Zs(i.from||n,i.default,!0):o=Zs(i.from||n):o=Zs(i),Ge(o)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>o.value,set:a=>o.value=a}):t[n]=o}}function Ao(e,t,s){xt(Z(e)?e.map(n=>n.bind(t.proxy)):e.bind(t.proxy),t,s)}function hl(e,t,s,n){let i=n.includes(".")?xl(s,n):()=>s[n];if(Ve(e)){const o=t[e];ie(o)&&vt(i,o)}else if(ie(e))vt(i,e.bind(s));else if(Le(e))if(Z(e))e.forEach(o=>hl(o,t,s,n));else{const o=ie(e.handler)?e.handler.bind(s):t[e.handler];ie(o)&&vt(i,o,e)}}function ao(e){const t=e.type,{mixins:s,extends:n}=t,{mixins:i,optionsCache:o,config:{optionMergeStrategies:a}}=e.appContext,l=o.get(t);let r;return l?r=l:!i.length&&!s&&!n?r=t:(r={},i.length&&i.forEach(u=>Un(r,u,a,!0)),Un(r,t,a)),Le(t)&&o.set(t,r),r}function Un(e,t,s,n=!1){const{mixins:i,extends:o}=t;o&&Un(e,o,s,!0),i&&i.forEach(a=>Un(e,a,s,!0));for(const a in t)if(!(n&&a==="expose")){const l=Dc[a]||s&&s[a];e[a]=l?l(e[a],t[a]):t[a]}return e}const Dc={data:No,props:Do,emits:Do,methods:Gs,computed:Gs,beforeCreate:st,created:st,beforeMount:st,mounted:st,beforeUpdate:st,updated:st,beforeDestroy:st,beforeUnmount:st,destroyed:st,unmounted:st,activated:st,deactivated:st,errorCaptured:st,serverPrefetch:st,components:Gs,directives:Gs,watch:Rc,provide:No,inject:Oc};function No(e,t){return t?e?function(){return Ke(ie(e)?e.call(this,this):e,ie(t)?t.call(this,this):t)}:t:e}function Oc(e,t){return Gs(Pi(e),Pi(t))}function Pi(e){if(Z(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function st(e,t){return e?[...new Set([].concat(e,t))]:t}function Gs(e,t){return e?Ke(Object.create(null),e,t):t}function Do(e,t){return e?Z(e)&&Z(t)?[...new Set([...e,...t])]:Ke(Object.create(null),Mo(e),Mo(t??{})):t}function Rc(e,t){if(!e)return t;if(!t)return e;const s=Ke(Object.create(null),e);for(const n in t)s[n]=st(e[n],t[n]);return s}function yl(){return{app:null,config:{isNativeTag:Tr,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Fc=0;function kc(e,t){return function(n,i=null){ie(n)||(n=Ke({},n)),i!=null&&!Le(i)&&(i=null);const o=yl(),a=new WeakSet,l=[];let r=!1;const u=o.app={_uid:Fc++,_component:n,_props:i,_container:null,_context:o,_instance:null,version:_f,get config(){return o.config},set config(f){},use(f,...d){return a.has(f)||(f&&ie(f.install)?(a.add(f),f.install(u,...d)):ie(f)&&(a.add(f),f(u,...d))),u},mixin(f){return o.mixins.includes(f)||o.mixins.push(f),u},component(f,d){return d?(o.components[f]=d,u):o.components[f]},directive(f,d){return d?(o.directives[f]=d,u):o.directives[f]},mount(f,d,h){if(!r){const T=u._ceVNode||Xe(n,i);return T.appContext=o,h===!0?h="svg":h===!1&&(h=void 0),d&&t?t(T,f):e(T,f,h),r=!0,u._container=f,f.__vue_app__=u,Zn(T.component)}},onUnmount(f){l.push(f)},unmount(){r&&(xt(l,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(f,d){return o.provides[f]=d,u},runWithContext(f){const d=As;As=u;try{return f()}finally{As=d}}};return u}}let As=null;function Uc(e,t){if(Qe){let s=Qe.provides;const n=Qe.parent&&Qe.parent.provides;n===s&&(s=Qe.provides=Object.create(n)),s[e]=t}}function Zs(e,t,s=!1){const n=Qe||ft;if(n||As){const i=As?As._context.provides:n?n.parent==null?n.vnode.appContext&&n.vnode.appContext.provides:n.parent.provides:void 0;if(i&&e in i)return i[e];if(arguments.length>1)return s&&ie(t)?t.call(n&&n.proxy):t}}const vl={},_l=()=>Object.create(vl),bl=e=>Object.getPrototypeOf(e)===vl;function Wc(e,t,s,n=!1){const i={},o=_l();e.propsDefaults=Object.create(null),Cl(e,t,i,o);for(const a in e.propsOptions[0])a in i||(i[a]=void 0);s?e.props=n?i:ic(i):e.type.props?e.props=i:e.props=o,e.attrs=o}function $c(e,t,s,n){const{props:i,attrs:o,vnode:{patchFlag:a}}=e,l=Ce(i),[r]=e.propsOptions;let u=!1;if((n||a>0)&&!(a&16)){if(a&8){const f=e.vnode.dynamicProps;for(let d=0;d<f.length;d++){let h=f[d];if(Qn(e.emitsOptions,h))continue;const T=t[h];if(r)if(be(o,h))T!==o[h]&&(o[h]=T,u=!0);else{const L=ps(h);i[L]=Ii(r,l,L,T,e,!1)}else T!==o[h]&&(o[h]=T,u=!0)}}}else{Cl(e,t,i,o)&&(u=!0);let f;for(const d in l)(!t||!be(t,d)&&((f=ms(d))===d||!be(t,f)))&&(r?s&&(s[d]!==void 0||s[f]!==void 0)&&(i[d]=Ii(r,l,d,void 0,e,!0)):delete i[d]);if(o!==l)for(const d in o)(!t||!be(t,d))&&(delete o[d],u=!0)}u&&Ht(e.attrs,"set","")}function Cl(e,t,s,n){const[i,o]=e.propsOptions;let a=!1,l;if(t)for(let r in t){if(Ks(r))continue;const u=t[r];let f;i&&be(i,f=ps(r))?!o||!o.includes(f)?s[f]=u:(l||(l={}))[f]=u:Qn(e.emitsOptions,r)||(!(r in n)||u!==n[r])&&(n[r]=u,a=!0)}if(o){const r=Ce(s),u=l||Pe;for(let f=0;f<o.length;f++){const d=o[f];s[d]=Ii(i,r,d,u[d],e,!be(u,d))}}return a}function Ii(e,t,s,n,i,o){const a=e[s];if(a!=null){const l=be(a,"default");if(l&&n===void 0){const r=a.default;if(a.type!==Function&&!a.skipFactory&&ie(r)){const{propsDefaults:u}=i;if(s in u)n=u[s];else{const f=yn(i);n=u[s]=r.call(null,t),f()}}else n=r;i.ce&&i.ce._setProp(s,n)}a[0]&&(o&&!l?n=!1:a[1]&&(n===""||n===ms(s))&&(n=!0))}return n}const Vc=new WeakMap;function Sl(e,t,s=!1){const n=s?Vc:t.propsCache,i=n.get(e);if(i)return i;const o=e.props,a={},l=[];let r=!1;if(!ie(e)){const f=d=>{r=!0;const[h,T]=Sl(d,t,!0);Ke(a,h),T&&l.push(...T)};!s&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}if(!o&&!r)return Le(e)&&n.set(e,Ps),Ps;if(Z(o))for(let f=0;f<o.length;f++){const d=ps(o[f]);Oo(d)&&(a[d]=Pe)}else if(o)for(const f in o){const d=ps(f);if(Oo(d)){const h=o[f],T=a[d]=Z(h)||ie(h)?{type:h}:Ke({},h),L=T.type;let M=!1,U=!0;if(Z(L))for(let y=0;y<L.length;++y){const w=L[y],x=ie(w)&&w.name;if(x==="Boolean"){M=!0;break}else x==="String"&&(U=!1)}else M=ie(L)&&L.name==="Boolean";T[0]=M,T[1]=U,(M||be(T,"default"))&&l.push(d)}}const u=[a,l];return Le(e)&&n.set(e,u),u}function Oo(e){return e[0]!=="$"&&!Ks(e)}const Tl=e=>e[0]==="_"||e==="$stable",lo=e=>Z(e)?e.map(Pt):[Pt(e)],Hc=(e,t,s)=>{if(t._n)return t;const n=hc((...i)=>lo(t(...i)),s);return n._c=!1,n},wl=(e,t,s)=>{const n=e._ctx;for(const i in e){if(Tl(i))continue;const o=e[i];if(ie(o))t[i]=Hc(i,o,n);else if(o!=null){const a=lo(o);t[i]=()=>a}}},El=(e,t)=>{const s=lo(t);e.slots.default=()=>s},zl=(e,t,s)=>{for(const n in t)(s||n!=="_")&&(e[n]=t[n])},jc=(e,t,s)=>{const n=e.slots=_l();if(e.vnode.shapeFlag&32){const i=t._;i?(zl(n,t,s),s&&Ua(n,"_",i,!0)):wl(t,n)}else t&&El(e,t)},Yc=(e,t,s)=>{const{vnode:n,slots:i}=e;let o=!0,a=Pe;if(n.shapeFlag&32){const l=t._;l?s&&l===1?o=!1:zl(i,t,s):(o=!t.$stable,wl(t,i)),a=t}else t&&(El(e,t),a={default:1});if(o)for(const l in i)!Tl(l)&&a[l]==null&&delete i[l]},lt=af;function Bc(e){return Xc(e)}function Xc(e,t){const s=Wa();s.__VUE__=!0;const{insert:n,remove:i,patchProp:o,createElement:a,createText:l,createComment:r,setText:u,setElementText:f,parentNode:d,nextSibling:h,setScopeId:T=It,insertStaticContent:L}=e,M=(m,v,P,W=null,k=null,c=null,g=void 0,S=null,I=!!v.dynamicChildren)=>{if(m===v)return;m&&!Ys(m,v)&&(W=ne(m),q(m,k,c,!0),m=null),v.patchFlag===-2&&(I=!1,v.dynamicChildren=null);const{type:N,ref:H,shapeFlag:_}=v;switch(N){case hn:U(m,v,P,W);break;case gs:y(m,v,P,W);break;case Mn:m==null&&w(v,P,W,g);break;case Fe:ce(m,v,P,W,k,c,g,S,I);break;default:_&1?D(m,v,P,W,k,c,g,S,I):_&6?je(m,v,P,W,k,c,g,S,I):(_&64||_&128)&&N.process(m,v,P,W,k,c,g,S,I,tt)}H!=null&&k&&wi(H,m&&m.ref,c,v||m,!v)},U=(m,v,P,W)=>{if(m==null)n(v.el=l(v.children),P,W);else{const k=v.el=m.el;v.children!==m.children&&u(k,v.children)}},y=(m,v,P,W)=>{m==null?n(v.el=r(v.children||""),P,W):v.el=m.el},w=(m,v,P,W)=>{[m.el,m.anchor]=L(m.children,v,P,W,m.el,m.anchor)},x=({el:m,anchor:v},P,W)=>{let k;for(;m&&m!==v;)k=h(m),n(m,P,W),m=k;n(v,P,W)},b=({el:m,anchor:v})=>{let P;for(;m&&m!==v;)P=h(m),i(m),m=P;i(v)},D=(m,v,P,W,k,c,g,S,I)=>{v.type==="svg"?g="svg":v.type==="math"&&(g="mathml"),m==null?$(v,P,W,k,c,g,S,I):fe(m,v,k,c,g,S,I)},$=(m,v,P,W,k,c,g,S)=>{let I,N;const{props:H,shapeFlag:_,transition:z,dirs:j}=m;if(I=m.el=a(m.type,c,H&&H.is,H),_&8?f(I,m.children):_&16&&se(m.children,I,null,W,k,di(m,c),g,S),j&&ls(m,null,W,"created"),R(I,m,m.scopeId,g,W),H){for(const oe in H)oe!=="value"&&!Ks(oe)&&o(I,oe,null,H[oe],c,W);"value"in H&&o(I,"value",null,H.value,c),(N=H.onVnodeBeforeMount)&&wt(N,W,m)}j&&ls(m,null,W,"beforeMount");const Q=Gc(k,z);Q&&z.beforeEnter(I),n(I,v,P),((N=H&&H.onVnodeMounted)||Q||j)&&lt(()=>{N&&wt(N,W,m),Q&&z.enter(I),j&&ls(m,null,W,"mounted")},k)},R=(m,v,P,W,k)=>{if(P&&T(m,P),W)for(let c=0;c<W.length;c++)T(m,W[c]);if(k){let c=k.subTree;if(v===c||Al(c.type)&&(c.ssContent===v||c.ssFallback===v)){const g=k.vnode;R(m,g,g.scopeId,g.slotScopeIds,k.parent)}}},se=(m,v,P,W,k,c,g,S,I=0)=>{for(let N=I;N<m.length;N++){const H=m[N]=S?Qt(m[N]):Pt(m[N]);M(null,H,v,P,W,k,c,g,S)}},fe=(m,v,P,W,k,c,g)=>{const S=v.el=m.el;let{patchFlag:I,dynamicChildren:N,dirs:H}=v;I|=m.patchFlag&16;const _=m.props||Pe,z=v.props||Pe;let j;if(P&&rs(P,!1),(j=z.onVnodeBeforeUpdate)&&wt(j,P,v,m),H&&ls(v,m,P,"beforeUpdate"),P&&rs(P,!0),(_.innerHTML&&z.innerHTML==null||_.textContent&&z.textContent==null)&&f(S,""),N?ve(m.dynamicChildren,N,S,P,W,di(v,k),c):g||re(m,v,S,null,P,W,di(v,k),c,!1),I>0){if(I&16)_e(S,_,z,P,k);else if(I&2&&_.class!==z.class&&o(S,"class",null,z.class,k),I&4&&o(S,"style",_.style,z.style,k),I&8){const Q=v.dynamicProps;for(let oe=0;oe<Q.length;oe++){const ae=Q[oe],Ee=_[ae],Ae=z[ae];(Ae!==Ee||ae==="value")&&o(S,ae,Ee,Ae,k,P)}}I&1&&m.children!==v.children&&f(S,v.children)}else!g&&N==null&&_e(S,_,z,P,k);((j=z.onVnodeUpdated)||H)&&lt(()=>{j&&wt(j,P,v,m),H&&ls(v,m,P,"updated")},W)},ve=(m,v,P,W,k,c,g)=>{for(let S=0;S<v.length;S++){const I=m[S],N=v[S],H=I.el&&(I.type===Fe||!Ys(I,N)||I.shapeFlag&70)?d(I.el):P;M(I,N,H,null,W,k,c,g,!0)}},_e=(m,v,P,W,k)=>{if(v!==P){if(v!==Pe)for(const c in v)!Ks(c)&&!(c in P)&&o(m,c,v[c],null,k,W);for(const c in P){if(Ks(c))continue;const g=P[c],S=v[c];g!==S&&c!=="value"&&o(m,c,S,g,k,W)}"value"in P&&o(m,"value",v.value,P.value,k)}},ce=(m,v,P,W,k,c,g,S,I)=>{const N=v.el=m?m.el:l(""),H=v.anchor=m?m.anchor:l("");let{patchFlag:_,dynamicChildren:z,slotScopeIds:j}=v;j&&(S=S?S.concat(j):j),m==null?(n(N,P,W),n(H,P,W),se(v.children||[],P,H,k,c,g,S,I)):_>0&&_&64&&z&&m.dynamicChildren?(ve(m.dynamicChildren,z,P,k,c,g,S),(v.key!=null||k&&v===k.subTree)&&Pl(m,v,!0)):re(m,v,P,H,k,c,g,S,I)},je=(m,v,P,W,k,c,g,S,I)=>{v.slotScopeIds=S,m==null?v.shapeFlag&512?k.ctx.activate(v,P,W,g,I):Ze(v,P,W,k,c,g,I):dt(m,v,I)},Ze=(m,v,P,W,k,c,g)=>{const S=m.component=df(m,W,k);if(gl(m)&&(S.ctx.renderer=tt),gf(S,!1,g),S.asyncDep){if(k&&k.registerDep(S,xe,g),!m.el){const I=S.subTree=Xe(gs);y(null,I,v,P)}}else xe(S,m,v,P,k,c,g)},dt=(m,v,P)=>{const W=v.component=m.component;if(nf(m,v,P))if(W.asyncDep&&!W.asyncResolved){ge(W,v,P);return}else W.next=v,W.update();else v.el=m.el,W.vnode=v},xe=(m,v,P,W,k,c,g)=>{const S=()=>{if(m.isMounted){let{next:_,bu:z,u:j,parent:Q,vnode:oe}=m;{const De=Il(m);if(De){_&&(_.el=oe.el,ge(m,_,g)),De.asyncDep.then(()=>{m.isUnmounted||S()});return}}let ae=_,Ee;rs(m,!1),_?(_.el=oe.el,ge(m,_,g)):_=oe,z&&xn(z),(Ee=_.props&&_.props.onVnodeBeforeUpdate)&&wt(Ee,Q,_,oe),rs(m,!0);const Ae=gi(m),Re=m.subTree;m.subTree=Ae,M(Re,Ae,d(Re.el),ne(Re),m,k,c),_.el=Ae.el,ae===null&&of(m,Ae.el),j&&lt(j,k),(Ee=_.props&&_.props.onVnodeUpdated)&&lt(()=>wt(Ee,Q,_,oe),k)}else{let _;const{el:z,props:j}=v,{bm:Q,m:oe,parent:ae,root:Ee,type:Ae}=m,Re=Qs(v);if(rs(m,!1),Q&&xn(Q),!Re&&(_=j&&j.onVnodeBeforeMount)&&wt(_,ae,v),rs(m,!0),z&&gt){const De=()=>{m.subTree=gi(m),gt(z,m.subTree,m,k,null)};Re&&Ae.__asyncHydrate?Ae.__asyncHydrate(z,m,De):De()}else{Ee.ce&&Ee.ce._injectChildStyle(Ae);const De=m.subTree=gi(m);M(null,De,P,W,m,k,c),v.el=De.el}if(oe&&lt(oe,k),!Re&&(_=j&&j.onVnodeMounted)){const De=v;lt(()=>wt(_,ae,De),k)}(v.shapeFlag&256||ae&&Qs(ae.vnode)&&ae.vnode.shapeFlag&256)&&m.a&&lt(m.a,k),m.isMounted=!0,v=P=W=null}};m.scope.on();const I=m.effect=new Ya(S);m.scope.off();const N=m.update=I.run.bind(I),H=m.job=I.runIfDirty.bind(I);H.i=m,H.id=m.uid,I.scheduler=()=>io(H),rs(m,!0),N()},ge=(m,v,P)=>{v.component=m;const W=m.vnode.props;m.vnode=v,m.next=null,$c(m,v.props,W,P),Yc(m,v.children,P),es(),xo(m),ts()},re=(m,v,P,W,k,c,g,S,I=!1)=>{const N=m&&m.children,H=m?m.shapeFlag:0,_=v.children,{patchFlag:z,shapeFlag:j}=v;if(z>0){if(z&128){et(N,_,P,W,k,c,g,S,I);return}else if(z&256){Me(N,_,P,W,k,c,g,S,I);return}}j&8?(H&16&&pe(N,k,c),_!==N&&f(P,_)):H&16?j&16?et(N,_,P,W,k,c,g,S,I):pe(N,k,c,!0):(H&8&&f(P,""),j&16&&se(_,P,W,k,c,g,S,I))},Me=(m,v,P,W,k,c,g,S,I)=>{m=m||Ps,v=v||Ps;const N=m.length,H=v.length,_=Math.min(N,H);let z;for(z=0;z<_;z++){const j=v[z]=I?Qt(v[z]):Pt(v[z]);M(m[z],j,P,null,k,c,g,S,I)}N>H?pe(m,k,c,!0,!1,_):se(v,P,W,k,c,g,S,I,_)},et=(m,v,P,W,k,c,g,S,I)=>{let N=0;const H=v.length;let _=m.length-1,z=H-1;for(;N<=_&&N<=z;){const j=m[N],Q=v[N]=I?Qt(v[N]):Pt(v[N]);if(Ys(j,Q))M(j,Q,P,null,k,c,g,S,I);else break;N++}for(;N<=_&&N<=z;){const j=m[_],Q=v[z]=I?Qt(v[z]):Pt(v[z]);if(Ys(j,Q))M(j,Q,P,null,k,c,g,S,I);else break;_--,z--}if(N>_){if(N<=z){const j=z+1,Q=j<H?v[j].el:W;for(;N<=z;)M(null,v[N]=I?Qt(v[N]):Pt(v[N]),P,Q,k,c,g,S,I),N++}}else if(N>z)for(;N<=_;)q(m[N],k,c,!0),N++;else{const j=N,Q=N,oe=new Map;for(N=Q;N<=z;N++){const Be=v[N]=I?Qt(v[N]):Pt(v[N]);Be.key!=null&&oe.set(Be.key,N)}let ae,Ee=0;const Ae=z-Q+1;let Re=!1,De=0;const At=new Array(Ae);for(N=0;N<Ae;N++)At[N]=0;for(N=j;N<=_;N++){const Be=m[N];if(Ee>=Ae){q(Be,k,c,!0);continue}let ot;if(Be.key!=null)ot=oe.get(Be.key);else for(ae=Q;ae<=z;ae++)if(At[ae-Q]===0&&Ys(Be,v[ae])){ot=ae;break}ot===void 0?q(Be,k,c,!0):(At[ot-Q]=N+1,ot>=De?De=ot:Re=!0,M(Be,v[ot],P,null,k,c,g,S,I),Ee++)}const vs=Re?Kc(At):Ps;for(ae=vs.length-1,N=Ae-1;N>=0;N--){const Be=Q+N,ot=v[Be],Nt=Be+1<H?v[Be+1].el:W;At[N]===0?M(null,ot,P,Nt,k,c,g,S,I):Re&&(ae<0||N!==vs[ae]?Ye(ot,P,Nt,2):ae--)}}},Ye=(m,v,P,W,k=null)=>{const{el:c,type:g,transition:S,children:I,shapeFlag:N}=m;if(N&6){Ye(m.component.subTree,v,P,W);return}if(N&128){m.suspense.move(v,P,W);return}if(N&64){g.move(m,v,P,tt);return}if(g===Fe){n(c,v,P);for(let _=0;_<I.length;_++)Ye(I[_],v,P,W);n(m.anchor,v,P);return}if(g===Mn){x(m,v,P);return}if(W!==2&&N&1&&S)if(W===0)S.beforeEnter(c),n(c,v,P),lt(()=>S.enter(c),k);else{const{leave:_,delayLeave:z,afterLeave:j}=S,Q=()=>n(c,v,P),oe=()=>{_(c,()=>{Q(),j&&j()})};z?z(c,Q,oe):oe()}else n(c,v,P)},q=(m,v,P,W=!1,k=!1)=>{const{type:c,props:g,ref:S,children:I,dynamicChildren:N,shapeFlag:H,patchFlag:_,dirs:z,cacheIndex:j}=m;if(_===-2&&(k=!1),S!=null&&wi(S,null,P,m,!0),j!=null&&(v.renderCache[j]=void 0),H&256){v.ctx.deactivate(m);return}const Q=H&1&&z,oe=!Qs(m);let ae;if(oe&&(ae=g&&g.onVnodeBeforeUnmount)&&wt(ae,v,m),H&6)Ie(m.component,P,W);else{if(H&128){m.suspense.unmount(P,W);return}Q&&ls(m,null,v,"beforeUnmount"),H&64?m.type.remove(m,v,P,tt,W):N&&!N.hasOnce&&(c!==Fe||_>0&&_&64)?pe(N,v,P,!1,!0):(c===Fe&&_&384||!k&&H&16)&&pe(I,v,P),W&&G(m)}(oe&&(ae=g&&g.onVnodeUnmounted)||Q)&&lt(()=>{ae&&wt(ae,v,m),Q&&ls(m,null,v,"unmounted")},P)},G=m=>{const{type:v,el:P,anchor:W,transition:k}=m;if(v===Fe){ee(P,W);return}if(v===Mn){b(m);return}const c=()=>{i(P),k&&!k.persisted&&k.afterLeave&&k.afterLeave()};if(m.shapeFlag&1&&k&&!k.persisted){const{leave:g,delayLeave:S}=k,I=()=>g(P,c);S?S(m.el,c,I):I()}else c()},ee=(m,v)=>{let P;for(;m!==v;)P=h(m),i(m),m=P;i(v)},Ie=(m,v,P)=>{const{bum:W,scope:k,job:c,subTree:g,um:S,m:I,a:N}=m;Ro(I),Ro(N),W&&xn(W),k.stop(),c&&(c.flags|=8,q(g,m,v,P)),S&&lt(S,v),lt(()=>{m.isUnmounted=!0},v),v&&v.pendingBranch&&!v.isUnmounted&&m.asyncDep&&!m.asyncResolved&&m.suspenseId===v.pendingId&&(v.deps--,v.deps===0&&v.resolve())},pe=(m,v,P,W=!1,k=!1,c=0)=>{for(let g=c;g<m.length;g++)q(m[g],v,P,W,k)},ne=m=>{if(m.shapeFlag&6)return ne(m.component.subTree);if(m.shapeFlag&128)return m.suspense.next();const v=h(m.anchor||m.el),P=v&&v[yc];return P?h(P):v};let Oe=!1;const Bt=(m,v,P)=>{m==null?v._vnode&&q(v._vnode,null,null,!0):M(v._vnode||null,m,v,null,null,null,P),v._vnode=m,Oe||(Oe=!0,xo(),fl(),Oe=!1)},tt={p:M,um:q,m:Ye,r:G,mt:Ze,mc:se,pc:re,pbc:ve,n:ne,o:e};let Mt,gt;return{render:Bt,hydrate:Mt,createApp:kc(Bt,Mt)}}function di({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function rs({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Gc(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Pl(e,t,s=!1){const n=e.children,i=t.children;if(Z(n)&&Z(i))for(let o=0;o<n.length;o++){const a=n[o];let l=i[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=i[o]=Qt(i[o]),l.el=a.el),!s&&l.patchFlag!==-2&&Pl(a,l)),l.type===hn&&(l.el=a.el)}}function Kc(e){const t=e.slice(),s=[0];let n,i,o,a,l;const r=e.length;for(n=0;n<r;n++){const u=e[n];if(u!==0){if(i=s[s.length-1],e[i]<u){t[n]=i,s.push(n);continue}for(o=0,a=s.length-1;o<a;)l=o+a>>1,e[s[l]]<u?o=l+1:a=l;u<e[s[o]]&&(o>0&&(t[n]=s[o-1]),s[o]=n)}}for(o=s.length,a=s[o-1];o-- >0;)s[o]=a,a=t[a];return s}function Il(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Il(t)}function Ro(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Jc=Symbol.for("v-scx"),Qc=()=>Zs(Jc);function vt(e,t,s){return Ll(e,t,s)}function Ll(e,t,s=Pe){const{immediate:n,deep:i,flush:o,once:a}=s,l=Ke({},s);let r;if(qn)if(o==="sync"){const h=Qc();r=h.__watcherHandles||(h.__watcherHandles=[])}else if(!t||n)l.once=!0;else{const h=()=>{};return h.stop=It,h.resume=It,h.pause=It,h}const u=Qe;l.call=(h,T,L)=>xt(h,u,T,L);let f=!1;o==="post"?l.scheduler=h=>{lt(h,u&&u.suspense)}:o!=="sync"&&(f=!0,l.scheduler=(h,T)=>{T?h():io(h)}),l.augmentJob=h=>{t&&(h.flags|=4),f&&(h.flags|=2,u&&(h.id=u.uid,h.i=u))};const d=pc(e,t,l);return r&&r.push(d),d}function qc(e,t,s){const n=this.proxy,i=Ve(e)?e.includes(".")?xl(n,e):()=>n[e]:e.bind(n,n);let o;ie(t)?o=t:(o=t.handler,s=t);const a=yn(this),l=Ll(i,o.bind(n),s);return a(),l}function xl(e,t){const s=t.split(".");return()=>{let n=e;for(let i=0;i<s.length&&n;i++)n=n[s[i]];return n}}const Zc=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${ps(t)}Modifiers`]||e[`${ms(t)}Modifiers`];function ef(e,t,...s){if(e.isUnmounted)return;const n=e.vnode.props||Pe;let i=s;const o=t.startsWith("update:"),a=o&&Zc(n,t.slice(7));a&&(a.trim&&(i=s.map(f=>Ve(f)?f.trim():f)),a.number&&(i=s.map(On)));let l,r=n[l=li(t)]||n[l=li(ps(t))];!r&&o&&(r=n[l=li(ms(t))]),r&&xt(r,e,6,i);const u=n[l+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,xt(u,e,6,i)}}function Ml(e,t,s=!1){const n=t.emitsCache,i=n.get(e);if(i!==void 0)return i;const o=e.emits;let a={},l=!1;if(!ie(e)){const r=u=>{const f=Ml(u,t,!0);f&&(l=!0,Ke(a,f))};!s&&t.mixins.length&&t.mixins.forEach(r),e.extends&&r(e.extends),e.mixins&&e.mixins.forEach(r)}return!o&&!l?(Le(e)&&n.set(e,null),null):(Z(o)?o.forEach(r=>a[r]=null):Ke(a,o),Le(e)&&n.set(e,a),a)}function Qn(e,t){return!e||!Hn(t)?!1:(t=t.slice(2).replace(/Once$/,""),be(e,t[0].toLowerCase()+t.slice(1))||be(e,ms(t))||be(e,t))}function gi(e){const{type:t,vnode:s,proxy:n,withProxy:i,propsOptions:[o],slots:a,attrs:l,emit:r,render:u,renderCache:f,props:d,data:h,setupState:T,ctx:L,inheritAttrs:M}=e,U=kn(e);let y,w;try{if(s.shapeFlag&4){const b=i||n,D=b;y=Pt(u.call(D,b,f,d,T,h,L)),w=l}else{const b=t;y=Pt(b.length>1?b(d,{attrs:l,slots:a,emit:r}):b(d,null)),w=t.props?l:tf(l)}}catch(b){en.length=0,Xn(b,e,1),y=Xe(gs)}let x=y;if(w&&M!==!1){const b=Object.keys(w),{shapeFlag:D}=x;b.length&&D&7&&(o&&b.some(Hi)&&(w=sf(w,o)),x=Ns(x,w,!1,!0))}return s.dirs&&(x=Ns(x,null,!1,!0),x.dirs=x.dirs?x.dirs.concat(s.dirs):s.dirs),s.transition&&oo(x,s.transition),y=x,kn(U),y}const tf=e=>{let t;for(const s in e)(s==="class"||s==="style"||Hn(s))&&((t||(t={}))[s]=e[s]);return t},sf=(e,t)=>{const s={};for(const n in e)(!Hi(n)||!(n.slice(9)in t))&&(s[n]=e[n]);return s};function nf(e,t,s){const{props:n,children:i,component:o}=e,{props:a,children:l,patchFlag:r}=t,u=o.emitsOptions;if(t.dirs||t.transition)return!0;if(s&&r>=0){if(r&1024)return!0;if(r&16)return n?Fo(n,a,u):!!a;if(r&8){const f=t.dynamicProps;for(let d=0;d<f.length;d++){const h=f[d];if(a[h]!==n[h]&&!Qn(u,h))return!0}}}else return(i||l)&&(!l||!l.$stable)?!0:n===a?!1:n?a?Fo(n,a,u):!0:!!a;return!1}function Fo(e,t,s){const n=Object.keys(t);if(n.length!==Object.keys(e).length)return!0;for(let i=0;i<n.length;i++){const o=n[i];if(t[o]!==e[o]&&!Qn(s,o))return!0}return!1}function of({vnode:e,parent:t},s){for(;t;){const n=t.subTree;if(n.suspense&&n.suspense.activeBranch===e&&(n.el=e.el),n===e)(e=t.vnode).el=s,t=t.parent;else break}}const Al=e=>e.__isSuspense;function af(e,t){t&&t.pendingBranch?Z(e)?t.effects.push(...e):t.effects.push(e):mc(e)}const Fe=Symbol.for("v-fgt"),hn=Symbol.for("v-txt"),gs=Symbol.for("v-cmt"),Mn=Symbol.for("v-stc"),en=[];let ut=null;function de(e=!1){en.push(ut=e?null:[])}function lf(){en.pop(),ut=en[en.length-1]||null}let an=1;function ko(e){an+=e,e<0&&ut&&(ut.hasOnce=!0)}function Nl(e){return e.dynamicChildren=an>0?ut||Ps:null,lf(),an>0&&ut&&ut.push(e),e}function me(e,t,s,n,i,o){return Nl(p(e,t,s,n,i,o,!0))}function Dl(e,t,s,n,i){return Nl(Xe(e,t,s,n,i,!0))}function Li(e){return e?e.__v_isVNode===!0:!1}function Ys(e,t){return e.type===t.type&&e.key===t.key}const Ol=({key:e})=>e??null,An=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?Ve(e)||Ge(e)||ie(e)?{i:ft,r:e,k:t,f:!!s}:e:null);function p(e,t=null,s=null,n=0,i=null,o=e===Fe?0:1,a=!1,l=!1){const r={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Ol(t),ref:t&&An(t),scopeId:pl,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:n,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:ft};return l?(ro(r,s),o&128&&e.normalize(r)):s&&(r.shapeFlag|=Ve(s)?8:16),an>0&&!a&&ut&&(r.patchFlag>0||o&6)&&r.patchFlag!==32&&ut.push(r),r}const Xe=rf;function rf(e,t=null,s=null,n=0,i=null,o=!1){if((!e||e===xc)&&(e=gs),Li(e)){const l=Ns(e,t,!0);return s&&ro(l,s),an>0&&!o&&ut&&(l.shapeFlag&6?ut[ut.indexOf(e)]=l:ut.push(l)),l.patchFlag=-2,l}if(vf(e)&&(e=e.__vccOpts),t){t=cf(t);let{class:l,style:r}=t;l&&!Ve(l)&&(t.class=Ue(l)),Le(r)&&(to(r)&&!Z(r)&&(r=Ke({},r)),t.style=Ls(r))}const a=Ve(e)?1:Al(e)?128:vc(e)?64:Le(e)?4:ie(e)?2:0;return p(e,t,s,n,i,a,o,!0)}function cf(e){return e?to(e)||bl(e)?Ke({},e):e:null}function Ns(e,t,s=!1,n=!1){const{props:i,ref:o,patchFlag:a,children:l,transition:r}=e,u=t?ff(i||{},t):i,f={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&Ol(u),ref:t&&t.ref?s&&o?Z(o)?o.concat(An(t)):[o,An(t)]:An(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Fe?a===-1?16:a|16:a,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:r,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ns(e.ssContent),ssFallback:e.ssFallback&&Ns(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return r&&n&&oo(f,r.clone(f)),f}function X(e=" ",t=0){return Xe(hn,null,e,t)}function zn(e,t){const s=Xe(Mn,null,e);return s.staticCount=t,s}function ct(e="",t=!1){return t?(de(),Dl(gs,null,e)):Xe(gs,null,e)}function Pt(e){return e==null||typeof e=="boolean"?Xe(gs):Z(e)?Xe(Fe,null,e.slice()):typeof e=="object"?Qt(e):Xe(hn,null,String(e))}function Qt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ns(e)}function ro(e,t){let s=0;const{shapeFlag:n}=e;if(t==null)t=null;else if(Z(t))s=16;else if(typeof t=="object")if(n&65){const i=t.default;i&&(i._c&&(i._d=!1),ro(e,i()),i._c&&(i._d=!0));return}else{s=32;const i=t._;!i&&!bl(t)?t._ctx=ft:i===3&&ft&&(ft.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else ie(t)?(t={default:t,_ctx:ft},s=32):(t=String(t),n&64?(s=16,t=[X(t)]):s=8);e.children=t,e.shapeFlag|=s}function ff(...e){const t={};for(let s=0;s<e.length;s++){const n=e[s];for(const i in n)if(i==="class")t.class!==n.class&&(t.class=Ue([t.class,n.class]));else if(i==="style")t.style=Ls([t.style,n.style]);else if(Hn(i)){const o=t[i],a=n[i];a&&o!==a&&!(Z(o)&&o.includes(a))&&(t[i]=o?[].concat(o,a):a)}else i!==""&&(t[i]=n[i])}return t}function wt(e,t,s,n=null){xt(e,t,7,[s,n])}const uf=yl();let pf=0;function df(e,t,s){const n=e.type,i=(t?t.appContext:e.appContext)||uf,o={uid:pf++,vnode:e,type:n,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new ja(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Sl(n,i),emitsOptions:Ml(n,i),emit:null,emitted:null,propsDefaults:Pe,inheritAttrs:n.inheritAttrs,ctx:Pe,data:Pe,props:Pe,attrs:Pe,slots:Pe,refs:Pe,setupState:Pe,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=ef.bind(null,o),e.ce&&e.ce(o),o}let Qe=null;const ln=()=>Qe||ft;let Wn,xi;{const e=Wa(),t=(s,n)=>{let i;return(i=e[s])||(i=e[s]=[]),i.push(n),o=>{i.length>1?i.forEach(a=>a(o)):i[0](o)}};Wn=t("__VUE_INSTANCE_SETTERS__",s=>Qe=s),xi=t("__VUE_SSR_SETTERS__",s=>qn=s)}const yn=e=>{const t=Qe;return Wn(e),e.scope.on(),()=>{e.scope.off(),Wn(t)}},Uo=()=>{Qe&&Qe.scope.off(),Wn(null)};function Rl(e){return e.vnode.shapeFlag&4}let qn=!1;function gf(e,t=!1,s=!1){t&&xi(t);const{props:n,children:i}=e.vnode,o=Rl(e);Wc(e,n,o,t),jc(e,i,s);const a=o?mf(e,t):void 0;return t&&xi(!1),a}function mf(e,t){const s=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Mc);const{setup:n}=s;if(n){const i=e.setupContext=n.length>1?yf(e):null,o=yn(e);es();const a=mn(n,e,0,[e.props,i]);if(ts(),o(),Oa(a)){if(Qs(e)||dl(e),a.then(Uo,Uo),t)return a.then(l=>{Wo(e,l,t)}).catch(l=>{Xn(l,e,0)});e.asyncDep=a}else Wo(e,a,t)}else Fl(e,t)}function Wo(e,t,s){ie(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Le(t)&&(e.setupState=ll(t)),Fl(e,s)}let $o;function Fl(e,t,s){const n=e.type;if(!e.render){if(!t&&$o&&!n.render){const i=n.template||ao(e).template;if(i){const{isCustomElement:o,compilerOptions:a}=e.appContext.config,{delimiters:l,compilerOptions:r}=n,u=Ke(Ke({isCustomElement:o,delimiters:l},a),r);n.render=$o(i,u)}}e.render=n.render||It}{const i=yn(e);es();try{Ac(e)}finally{ts(),i()}}}const hf={get(e,t){return qe(e,"get",""),e[t]}};function yf(e){const t=s=>{e.exposed=s||{}};return{attrs:new Proxy(e.attrs,hf),slots:e.slots,emit:e.emit,expose:t}}function Zn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(ll(oc(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in qs)return qs[s](e)},has(t,s){return s in t||s in qs}})):e.proxy}function vf(e){return ie(e)&&"__vccOpts"in e}const ws=(e,t)=>fc(e,t,qn);function kl(e,t,s){const n=arguments.length;return n===2?Le(t)&&!Z(t)?Li(t)?Xe(e,null,[t]):Xe(e,t):Xe(e,null,t):(n>3?s=Array.prototype.slice.call(arguments,2):n===3&&Li(s)&&(s=[s]),Xe(e,t,s))}const _f="3.5.8";/**
* @vue/runtime-dom v3.5.8
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Mi;const Vo=typeof window<"u"&&window.trustedTypes;if(Vo)try{Mi=Vo.createPolicy("vue",{createHTML:e=>e})}catch{}const Ul=Mi?e=>Mi.createHTML(e):e=>e,bf="http://www.w3.org/2000/svg",Cf="http://www.w3.org/1998/Math/MathML",Ut=typeof document<"u"?document:null,Ho=Ut&&Ut.createElement("template"),Sf={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,n)=>{const i=t==="svg"?Ut.createElementNS(bf,e):t==="mathml"?Ut.createElementNS(Cf,e):s?Ut.createElement(e,{is:s}):Ut.createElement(e);return e==="select"&&n&&n.multiple!=null&&i.setAttribute("multiple",n.multiple),i},createText:e=>Ut.createTextNode(e),createComment:e=>Ut.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ut.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,n,i,o){const a=s?s.previousSibling:t.lastChild;if(i&&(i===o||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),s),!(i===o||!(i=i.nextSibling)););else{Ho.innerHTML=Ul(n==="svg"?`<svg>${e}</svg>`:n==="mathml"?`<math>${e}</math>`:e);const l=Ho.content;if(n==="svg"||n==="mathml"){const r=l.firstChild;for(;r.firstChild;)l.appendChild(r.firstChild);l.removeChild(r)}t.insertBefore(l,s)}return[a?a.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},Tf=Symbol("_vtc");function wf(e,t,s){const n=e[Tf];n&&(t=(t?[t,...n]:[...n]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const $n=Symbol("_vod"),Wl=Symbol("_vsh"),Ct={beforeMount(e,{value:t},{transition:s}){e[$n]=e.style.display==="none"?"":e.style.display,s&&t?s.beforeEnter(e):Bs(e,t)},mounted(e,{value:t},{transition:s}){s&&t&&s.enter(e)},updated(e,{value:t,oldValue:s},{transition:n}){!t!=!s&&(n?t?(n.beforeEnter(e),Bs(e,!0),n.enter(e)):n.leave(e,()=>{Bs(e,!1)}):Bs(e,t))},beforeUnmount(e,{value:t}){Bs(e,t)}};function Bs(e,t){e.style.display=t?e[$n]:"none",e[Wl]=!t}const Ef=Symbol(""),zf=/(^|;)\s*display\s*:/;function Pf(e,t,s){const n=e.style,i=Ve(s);let o=!1;if(s&&!i){if(t)if(Ve(t))for(const a of t.split(";")){const l=a.slice(0,a.indexOf(":")).trim();s[l]==null&&Nn(n,l,"")}else for(const a in t)s[a]==null&&Nn(n,a,"");for(const a in s)a==="display"&&(o=!0),Nn(n,a,s[a])}else if(i){if(t!==s){const a=n[Ef];a&&(s+=";"+a),n.cssText=s,o=zf.test(s)}}else t&&e.removeAttribute("style");$n in e&&(e[$n]=o?n.display:"",e[Wl]&&(n.display="none"))}const jo=/\s*!important$/;function Nn(e,t,s){if(Z(s))s.forEach(n=>Nn(e,t,n));else if(s==null&&(s=""),t.startsWith("--"))e.setProperty(t,s);else{const n=If(e,t);jo.test(s)?e.setProperty(ms(n),s.replace(jo,""),"important"):e[n]=s}}const Yo=["Webkit","Moz","ms"],mi={};function If(e,t){const s=mi[t];if(s)return s;let n=ps(t);if(n!=="filter"&&n in e)return mi[t]=n;n=ka(n);for(let i=0;i<Yo.length;i++){const o=Yo[i]+n;if(o in e)return mi[t]=o}return t}const Bo="http://www.w3.org/1999/xlink";function Xo(e,t,s,n,i,o=Nr(t)){n&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(Bo,t.slice(6,t.length)):e.setAttributeNS(Bo,t,s):s==null||o&&!$a(s)?e.removeAttribute(t):e.setAttribute(t,o?"":Lt(s)?String(s):s)}function Lf(e,t,s,n){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?Ul(s):s);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const a=i==="OPTION"?e.getAttribute("value")||"":e.value,l=s==null?e.type==="checkbox"?"on":"":String(s);(a!==l||!("_value"in e))&&(e.value=l),s==null&&e.removeAttribute(t),e._value=s;return}let o=!1;if(s===""||s==null){const a=typeof e[t];a==="boolean"?s=$a(s):s==null&&a==="string"?(s="",o=!0):a==="number"&&(s=0,o=!0)}try{e[t]=s}catch{}o&&e.removeAttribute(t)}function qt(e,t,s,n){e.addEventListener(t,s,n)}function xf(e,t,s,n){e.removeEventListener(t,s,n)}const Go=Symbol("_vei");function Mf(e,t,s,n,i=null){const o=e[Go]||(e[Go]={}),a=o[t];if(n&&a)a.value=n;else{const[l,r]=Af(t);if(n){const u=o[t]=Of(n,i);qt(e,l,u,r)}else a&&(xf(e,l,a,r),o[t]=void 0)}}const Ko=/(?:Once|Passive|Capture)$/;function Af(e){let t;if(Ko.test(e)){t={};let n;for(;n=e.match(Ko);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):ms(e.slice(2)),t]}let hi=0;const Nf=Promise.resolve(),Df=()=>hi||(Nf.then(()=>hi=0),hi=Date.now());function Of(e,t){const s=n=>{if(!n._vts)n._vts=Date.now();else if(n._vts<=s.attached)return;xt(Rf(n,s.value),t,5,[n])};return s.value=e,s.attached=Df(),s}function Rf(e,t){if(Z(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(n=>i=>!i._stopped&&n&&n(i))}else return t}const Jo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Ff=(e,t,s,n,i,o)=>{const a=i==="svg";t==="class"?wf(e,n,a):t==="style"?Pf(e,s,n):Hn(t)?Hi(t)||Mf(e,t,s,n,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):kf(e,t,n,a))?(Lf(e,t,n),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Xo(e,t,n,a,o,t!=="value")):(t==="true-value"?e._trueValue=n:t==="false-value"&&(e._falseValue=n),Xo(e,t,n,a))};function kf(e,t,s,n){if(n)return!!(t==="innerHTML"||t==="textContent"||t in e&&Jo(t)&&ie(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const i=e.tagName;if(i==="IMG"||i==="VIDEO"||i==="CANVAS"||i==="SOURCE")return!1}return Jo(t)&&Ve(s)?!1:!!(t in e||e._isVueCE&&(/[A-Z]/.test(t)||!Ve(s)))}const Ds=e=>{const t=e.props["onUpdate:modelValue"]||!1;return Z(t)?s=>xn(t,s):t};function Uf(e){e.target.composing=!0}function Qo(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const jt=Symbol("_assign"),te={created(e,{modifiers:{lazy:t,trim:s,number:n}},i){e[jt]=Ds(i);const o=n||i.props&&i.props.type==="number";qt(e,t?"change":"input",a=>{if(a.target.composing)return;let l=e.value;s&&(l=l.trim()),o&&(l=On(l)),e[jt](l)}),s&&qt(e,"change",()=>{e.value=e.value.trim()}),t||(qt(e,"compositionstart",Uf),qt(e,"compositionend",Qo),qt(e,"change",Qo))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:n,trim:i,number:o}},a){if(e[jt]=Ds(a),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?On(e.value):e.value,r=t??"";l!==r&&(document.activeElement===e&&e.type!=="range"&&(n&&t===s||i&&e.value.trim()===r)||(e.value=r))}},kt={deep:!0,created(e,t,s){e[jt]=Ds(s),qt(e,"change",()=>{const n=e._modelValue,i=rn(e),o=e.checked,a=e[jt];if(Z(n)){const l=Bi(n,i),r=l!==-1;if(o&&!r)a(n.concat(i));else if(!o&&r){const u=[...n];u.splice(l,1),a(u)}}else if(ks(n)){const l=new Set(n);o?l.add(i):l.delete(i),a(l)}else a($l(e,o))})},mounted:qo,beforeUpdate(e,t,s){e[jt]=Ds(s),qo(e,t,s)}};function qo(e,{value:t,oldValue:s},n){e._modelValue=t;let i;Z(t)?i=Bi(t,n.props.value)>-1:ks(t)?i=t.has(n.props.value):i=gn(t,$l(e,!0)),e.checked!==i&&(e.checked=i)}const Gt={deep:!0,created(e,{value:t,modifiers:{number:s}},n){const i=ks(t);qt(e,"change",()=>{const o=Array.prototype.filter.call(e.options,a=>a.selected).map(a=>s?On(rn(a)):rn(a));e[jt](e.multiple?i?new Set(o):o:o[0]),e._assigning=!0,zs(()=>{e._assigning=!1})}),e[jt]=Ds(n)},mounted(e,{value:t,modifiers:{number:s}}){Zo(e,t)},beforeUpdate(e,t,s){e[jt]=Ds(s)},updated(e,{value:t,modifiers:{number:s}}){e._assigning||Zo(e,t)}};function Zo(e,t,s){const n=e.multiple,i=Z(t);if(!(n&&!i&&!ks(t))){for(let o=0,a=e.options.length;o<a;o++){const l=e.options[o],r=rn(l);if(n)if(i){const u=typeof r;u==="string"||u==="number"?l.selected=t.some(f=>String(f)===String(r)):l.selected=Bi(t,r)>-1}else l.selected=t.has(r);else if(gn(rn(l),t)){e.selectedIndex!==o&&(e.selectedIndex=o);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function rn(e){return"_value"in e?e._value:e.value}function $l(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const Wf=Ke({patchProp:Ff},Sf);let ea;function $f(){return ea||(ea=Bc(Wf))}const Vf=(...e)=>{const t=$f().createApp(...e),{mount:s}=t;return t.mount=n=>{const i=jf(n);if(!i)return;const o=t._component;!ie(o)&&!o.render&&!o.template&&(o.template=i.innerHTML),i.nodeType===1&&(i.textContent="");const a=s(i,!1,Hf(i));return i instanceof Element&&(i.removeAttribute("v-cloak"),i.setAttribute("data-v-app","")),a},t};function Hf(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function jf(e){return Ve(e)?document.querySelector(e):e}/*!
  * shared v11.0.0-rc.1
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */const Vn=typeof window<"u",ss=(e,t=!1)=>t?Symbol.for(e):Symbol(e),Yf=(e,t,s)=>Bf({l:e,k:t,s}),Bf=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),We=e=>typeof e=="number"&&isFinite(e),Xf=e=>co(e)==="[object Date]",Os=e=>co(e)==="[object RegExp]",ei=e=>ue(e)&&Object.keys(e).length===0,He=Object.assign,Gf=Object.create,we=(e=null)=>Gf(e);let ta;const fs=()=>ta||(ta=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:we());function sa(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const Kf=Object.prototype.hasOwnProperty;function St(e,t){return Kf.call(e,t)}const $e=Array.isArray,Ne=e=>typeof e=="function",K=e=>typeof e=="string",he=e=>typeof e=="boolean",ye=e=>e!==null&&typeof e=="object",Jf=e=>ye(e)&&Ne(e.then)&&Ne(e.catch),Vl=Object.prototype.toString,co=e=>Vl.call(e),ue=e=>co(e)==="[object Object]",Qf=e=>e==null?"":$e(e)||ue(e)&&e.toString===Vl?JSON.stringify(e,null,2):String(e);function fo(e,t=""){return e.reduce((s,n,i)=>i===0?s+n:s+t+n,"")}function qf(e,t){typeof console<"u"&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const Pn=e=>!ye(e)||$e(e);function Dn(e,t){if(Pn(e)||Pn(t))throw new Error("Invalid value");const s=[{src:e,des:t}];for(;s.length;){const{src:n,des:i}=s.pop();Object.keys(n).forEach(o=>{o!=="__proto__"&&(ye(n[o])&&!ye(i[o])&&(i[o]=Array.isArray(n[o])?[]:we()),Pn(i[o])||Pn(n[o])?i[o]=n[o]:s.push({src:n[o],des:i[o]}))})}}/*!
  * message-compiler v11.0.0-rc.1
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */function Zf(e,t,s){return{line:e,column:t,offset:s}}function Ai(e,t,s){return{start:e,end:t}}const Se={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16},eu=17;function ti(e,t,s={}){const{domain:n,messages:i,args:o}=s,a=e,l=new SyntaxError(String(a));return l.code=e,t&&(l.location=t),l.domain=n,l}function tu(e){throw e}const Ot=" ",su="\r",nt=`
`,nu="\u2028",iu="\u2029";function ou(e){const t=e;let s=0,n=1,i=1,o=0;const a=R=>t[R]===su&&t[R+1]===nt,l=R=>t[R]===nt,r=R=>t[R]===iu,u=R=>t[R]===nu,f=R=>a(R)||l(R)||r(R)||u(R),d=()=>s,h=()=>n,T=()=>i,L=()=>o,M=R=>a(R)||r(R)||u(R)?nt:t[R],U=()=>M(s),y=()=>M(s+o);function w(){return o=0,f(s)&&(n++,i=0),a(s)&&s++,s++,i++,t[s]}function x(){return a(s+o)&&o++,o++,t[s+o]}function b(){s=0,n=1,i=1,o=0}function D(R=0){o=R}function $(){const R=s+o;for(;R!==s;)w();o=0}return{index:d,line:h,column:T,peekOffset:L,charAt:M,currentChar:U,currentPeek:y,next:w,peek:x,reset:b,resetPeek:D,skipToPeek:$}}const Kt=void 0,au=".",na="'",lu="tokenizer";function ru(e,t={}){const s=t.location!==!1,n=ou(e),i=()=>n.index(),o=()=>Zf(n.line(),n.column(),n.index()),a=o(),l=i(),r={currentType:13,offset:l,startLoc:a,endLoc:a,lastType:13,lastOffset:l,lastStartLoc:a,lastEndLoc:a,braceNest:0,inLinked:!1,text:""},u=()=>r,{onError:f}=t;function d(c,g,S,...I){const N=u();if(g.column+=S,g.offset+=S,f){const H=s?Ai(N.startLoc,g):null,_=ti(c,H,{domain:lu,args:I});f(_)}}function h(c,g,S){c.endLoc=o(),c.currentType=g;const I={type:g};return s&&(I.loc=Ai(c.startLoc,c.endLoc)),S!=null&&(I.value=S),I}const T=c=>h(c,13);function L(c,g){return c.currentChar()===g?(c.next(),g):(d(Se.EXPECTED_TOKEN,o(),0,g),"")}function M(c){let g="";for(;c.currentPeek()===Ot||c.currentPeek()===nt;)g+=c.currentPeek(),c.peek();return g}function U(c){const g=M(c);return c.skipToPeek(),g}function y(c){if(c===Kt)return!1;const g=c.charCodeAt(0);return g>=97&&g<=122||g>=65&&g<=90||g===95}function w(c){if(c===Kt)return!1;const g=c.charCodeAt(0);return g>=48&&g<=57}function x(c,g){const{currentType:S}=g;if(S!==2)return!1;M(c);const I=y(c.currentPeek());return c.resetPeek(),I}function b(c,g){const{currentType:S}=g;if(S!==2)return!1;M(c);const I=c.currentPeek()==="-"?c.peek():c.currentPeek(),N=w(I);return c.resetPeek(),N}function D(c,g){const{currentType:S}=g;if(S!==2)return!1;M(c);const I=c.currentPeek()===na;return c.resetPeek(),I}function $(c,g){const{currentType:S}=g;if(S!==7)return!1;M(c);const I=c.currentPeek()===".";return c.resetPeek(),I}function R(c,g){const{currentType:S}=g;if(S!==8)return!1;M(c);const I=y(c.currentPeek());return c.resetPeek(),I}function se(c,g){const{currentType:S}=g;if(!(S===7||S===11))return!1;M(c);const I=c.currentPeek()===":";return c.resetPeek(),I}function fe(c,g){const{currentType:S}=g;if(S!==9)return!1;const I=()=>{const H=c.currentPeek();return H==="{"?y(c.peek()):H==="@"||H==="|"||H===":"||H==="."||H===Ot||!H?!1:H===nt?(c.peek(),I()):_e(c,!1)},N=I();return c.resetPeek(),N}function ve(c){M(c);const g=c.currentPeek()==="|";return c.resetPeek(),g}function _e(c,g=!0){const S=(N=!1,H="")=>{const _=c.currentPeek();return _==="{"||_==="@"||!_?N:_==="|"?!(H===Ot||H===nt):_===Ot?(c.peek(),S(!0,Ot)):_===nt?(c.peek(),S(!0,nt)):!0},I=S();return g&&c.resetPeek(),I}function ce(c,g){const S=c.currentChar();return S===Kt?Kt:g(S)?(c.next(),S):null}function je(c){const g=c.charCodeAt(0);return g>=97&&g<=122||g>=65&&g<=90||g>=48&&g<=57||g===95||g===36}function Ze(c){return ce(c,je)}function dt(c){const g=c.charCodeAt(0);return g>=97&&g<=122||g>=65&&g<=90||g>=48&&g<=57||g===95||g===36||g===45}function xe(c){return ce(c,dt)}function ge(c){const g=c.charCodeAt(0);return g>=48&&g<=57}function re(c){return ce(c,ge)}function Me(c){const g=c.charCodeAt(0);return g>=48&&g<=57||g>=65&&g<=70||g>=97&&g<=102}function et(c){return ce(c,Me)}function Ye(c){let g="",S="";for(;g=re(c);)S+=g;return S}function q(c){let g="";for(;;){const S=c.currentChar();if(S==="{"||S==="}"||S==="@"||S==="|"||!S)break;if(S===Ot||S===nt)if(_e(c))g+=S,c.next();else{if(ve(c))break;g+=S,c.next()}else g+=S,c.next()}return g}function G(c){U(c);let g="",S="";for(;g=xe(c);)S+=g;return c.currentChar()===Kt&&d(Se.UNTERMINATED_CLOSING_BRACE,o(),0),S}function ee(c){U(c);let g="";return c.currentChar()==="-"?(c.next(),g+=`-${Ye(c)}`):g+=Ye(c),c.currentChar()===Kt&&d(Se.UNTERMINATED_CLOSING_BRACE,o(),0),g}function Ie(c){return c!==na&&c!==nt}function pe(c){U(c),L(c,"'");let g="",S="";for(;g=ce(c,Ie);)g==="\\"?S+=ne(c):S+=g;const I=c.currentChar();return I===nt||I===Kt?(d(Se.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,o(),0),I===nt&&(c.next(),L(c,"'")),S):(L(c,"'"),S)}function ne(c){const g=c.currentChar();switch(g){case"\\":case"'":return c.next(),`\\${g}`;case"u":return Oe(c,g,4);case"U":return Oe(c,g,6);default:return d(Se.UNKNOWN_ESCAPE_SEQUENCE,o(),0,g),""}}function Oe(c,g,S){L(c,g);let I="";for(let N=0;N<S;N++){const H=et(c);if(!H){d(Se.INVALID_UNICODE_ESCAPE_SEQUENCE,o(),0,`\\${g}${I}${c.currentChar()}`);break}I+=H}return`\\${g}${I}`}function Bt(c){return c!=="{"&&c!=="}"&&c!==Ot&&c!==nt}function tt(c){U(c);let g="",S="";for(;g=ce(c,Bt);)S+=g;return S}function Mt(c){let g="",S="";for(;g=Ze(c);)S+=g;return S}function gt(c){const g=S=>{const I=c.currentChar();return I==="{"||I==="@"||I==="|"||I==="("||I===")"||!I||I===Ot?S:(S+=I,c.next(),g(S))};return g("")}function m(c){U(c);const g=L(c,"|");return U(c),g}function v(c,g){let S=null;switch(c.currentChar()){case"{":return g.braceNest>=1&&d(Se.NOT_ALLOW_NEST_PLACEHOLDER,o(),0),c.next(),S=h(g,2,"{"),U(c),g.braceNest++,S;case"}":return g.braceNest>0&&g.currentType===2&&d(Se.EMPTY_PLACEHOLDER,o(),0),c.next(),S=h(g,3,"}"),g.braceNest--,g.braceNest>0&&U(c),g.inLinked&&g.braceNest===0&&(g.inLinked=!1),S;case"@":return g.braceNest>0&&d(Se.UNTERMINATED_CLOSING_BRACE,o(),0),S=P(c,g)||T(g),g.braceNest=0,S;default:{let N=!0,H=!0,_=!0;if(ve(c))return g.braceNest>0&&d(Se.UNTERMINATED_CLOSING_BRACE,o(),0),S=h(g,1,m(c)),g.braceNest=0,g.inLinked=!1,S;if(g.braceNest>0&&(g.currentType===4||g.currentType===5||g.currentType===6))return d(Se.UNTERMINATED_CLOSING_BRACE,o(),0),g.braceNest=0,W(c,g);if(N=x(c,g))return S=h(g,4,G(c)),U(c),S;if(H=b(c,g))return S=h(g,5,ee(c)),U(c),S;if(_=D(c,g))return S=h(g,6,pe(c)),U(c),S;if(!N&&!H&&!_)return S=h(g,12,tt(c)),d(Se.INVALID_TOKEN_IN_PLACEHOLDER,o(),0,S.value),U(c),S;break}}return S}function P(c,g){const{currentType:S}=g;let I=null;const N=c.currentChar();switch((S===7||S===8||S===11||S===9)&&(N===nt||N===Ot)&&d(Se.INVALID_LINKED_FORMAT,o(),0),N){case"@":return c.next(),I=h(g,7,"@"),g.inLinked=!0,I;case".":return U(c),c.next(),h(g,8,".");case":":return U(c),c.next(),h(g,9,":");default:return ve(c)?(I=h(g,1,m(c)),g.braceNest=0,g.inLinked=!1,I):$(c,g)||se(c,g)?(U(c),P(c,g)):R(c,g)?(U(c),h(g,11,Mt(c))):fe(c,g)?(U(c),N==="{"?v(c,g)||I:h(g,10,gt(c))):(S===7&&d(Se.INVALID_LINKED_FORMAT,o(),0),g.braceNest=0,g.inLinked=!1,W(c,g))}}function W(c,g){let S={type:13};if(g.braceNest>0)return v(c,g)||T(g);if(g.inLinked)return P(c,g)||T(g);switch(c.currentChar()){case"{":return v(c,g)||T(g);case"}":return d(Se.UNBALANCED_CLOSING_BRACE,o(),0),c.next(),h(g,3,"}");case"@":return P(c,g)||T(g);default:{if(ve(c))return S=h(g,1,m(c)),g.braceNest=0,g.inLinked=!1,S;if(_e(c))return h(g,0,q(c));break}}return S}function k(){const{currentType:c,offset:g,startLoc:S,endLoc:I}=r;return r.lastType=c,r.lastOffset=g,r.lastStartLoc=S,r.lastEndLoc=I,r.offset=i(),r.startLoc=o(),n.currentChar()===Kt?h(r,13):W(n,r)}return{nextToken:k,currentOffset:i,currentPosition:o,context:u}}const cu="parser",fu=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function uu(e,t,s){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const n=parseInt(t||s,16);return n<=55295||n>=57344?String.fromCodePoint(n):"�"}}}function pu(e={}){const t=e.location!==!1,{onError:s}=e;function n(y,w,x,b,...D){const $=y.currentPosition();if($.offset+=b,$.column+=b,s){const R=t?Ai(x,$):null,se=ti(w,R,{domain:cu,args:D});s(se)}}function i(y,w,x){const b={type:y};return t&&(b.start=w,b.end=w,b.loc={start:x,end:x}),b}function o(y,w,x,b){t&&(y.end=w,y.loc&&(y.loc.end=x))}function a(y,w){const x=y.context(),b=i(3,x.offset,x.startLoc);return b.value=w,o(b,y.currentOffset(),y.currentPosition()),b}function l(y,w){const x=y.context(),{lastOffset:b,lastStartLoc:D}=x,$=i(5,b,D);return $.index=parseInt(w,10),y.nextToken(),o($,y.currentOffset(),y.currentPosition()),$}function r(y,w){const x=y.context(),{lastOffset:b,lastStartLoc:D}=x,$=i(4,b,D);return $.key=w,y.nextToken(),o($,y.currentOffset(),y.currentPosition()),$}function u(y,w){const x=y.context(),{lastOffset:b,lastStartLoc:D}=x,$=i(9,b,D);return $.value=w.replace(fu,uu),y.nextToken(),o($,y.currentOffset(),y.currentPosition()),$}function f(y){const w=y.nextToken(),x=y.context(),{lastOffset:b,lastStartLoc:D}=x,$=i(8,b,D);return w.type!==11?(n(y,Se.UNEXPECTED_EMPTY_LINKED_MODIFIER,x.lastStartLoc,0),$.value="",o($,b,D),{nextConsumeToken:w,node:$}):(w.value==null&&n(y,Se.UNEXPECTED_LEXICAL_ANALYSIS,x.lastStartLoc,0,Et(w)),$.value=w.value||"",o($,y.currentOffset(),y.currentPosition()),{node:$})}function d(y,w){const x=y.context(),b=i(7,x.offset,x.startLoc);return b.value=w,o(b,y.currentOffset(),y.currentPosition()),b}function h(y){const w=y.context(),x=i(6,w.offset,w.startLoc);let b=y.nextToken();if(b.type===8){const D=f(y);x.modifier=D.node,b=D.nextConsumeToken||y.nextToken()}switch(b.type!==9&&n(y,Se.UNEXPECTED_LEXICAL_ANALYSIS,w.lastStartLoc,0,Et(b)),b=y.nextToken(),b.type===2&&(b=y.nextToken()),b.type){case 10:b.value==null&&n(y,Se.UNEXPECTED_LEXICAL_ANALYSIS,w.lastStartLoc,0,Et(b)),x.key=d(y,b.value||"");break;case 4:b.value==null&&n(y,Se.UNEXPECTED_LEXICAL_ANALYSIS,w.lastStartLoc,0,Et(b)),x.key=r(y,b.value||"");break;case 5:b.value==null&&n(y,Se.UNEXPECTED_LEXICAL_ANALYSIS,w.lastStartLoc,0,Et(b)),x.key=l(y,b.value||"");break;case 6:b.value==null&&n(y,Se.UNEXPECTED_LEXICAL_ANALYSIS,w.lastStartLoc,0,Et(b)),x.key=u(y,b.value||"");break;default:{n(y,Se.UNEXPECTED_EMPTY_LINKED_KEY,w.lastStartLoc,0);const D=y.context(),$=i(7,D.offset,D.startLoc);return $.value="",o($,D.offset,D.startLoc),x.key=$,o(x,D.offset,D.startLoc),{nextConsumeToken:b,node:x}}}return o(x,y.currentOffset(),y.currentPosition()),{node:x}}function T(y){const w=y.context(),x=w.currentType===1?y.currentOffset():w.offset,b=w.currentType===1?w.endLoc:w.startLoc,D=i(2,x,b);D.items=[];let $=null;do{const fe=$||y.nextToken();switch($=null,fe.type){case 0:fe.value==null&&n(y,Se.UNEXPECTED_LEXICAL_ANALYSIS,w.lastStartLoc,0,Et(fe)),D.items.push(a(y,fe.value||""));break;case 5:fe.value==null&&n(y,Se.UNEXPECTED_LEXICAL_ANALYSIS,w.lastStartLoc,0,Et(fe)),D.items.push(l(y,fe.value||""));break;case 4:fe.value==null&&n(y,Se.UNEXPECTED_LEXICAL_ANALYSIS,w.lastStartLoc,0,Et(fe)),D.items.push(r(y,fe.value||""));break;case 6:fe.value==null&&n(y,Se.UNEXPECTED_LEXICAL_ANALYSIS,w.lastStartLoc,0,Et(fe)),D.items.push(u(y,fe.value||""));break;case 7:{const ve=h(y);D.items.push(ve.node),$=ve.nextConsumeToken||null;break}}}while(w.currentType!==13&&w.currentType!==1);const R=w.currentType===1?w.lastOffset:y.currentOffset(),se=w.currentType===1?w.lastEndLoc:y.currentPosition();return o(D,R,se),D}function L(y,w,x,b){const D=y.context();let $=b.items.length===0;const R=i(1,w,x);R.cases=[],R.cases.push(b);do{const se=T(y);$||($=se.items.length===0),R.cases.push(se)}while(D.currentType!==13);return $&&n(y,Se.MUST_HAVE_MESSAGES_IN_PLURAL,x,0),o(R,y.currentOffset(),y.currentPosition()),R}function M(y){const w=y.context(),{offset:x,startLoc:b}=w,D=T(y);return w.currentType===13?D:L(y,x,b,D)}function U(y){const w=ru(y,He({},e)),x=w.context(),b=i(0,x.offset,x.startLoc);return t&&b.loc&&(b.loc.source=y),b.body=M(w),e.onCacheKey&&(b.cacheKey=e.onCacheKey(y)),x.currentType!==13&&n(w,Se.UNEXPECTED_LEXICAL_ANALYSIS,x.lastStartLoc,0,y[x.offset]||""),o(b,w.currentOffset(),w.currentPosition()),b}return{parse:U}}function Et(e){if(e.type===13)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function du(e,t={}){const s={ast:e,helpers:new Set};return{context:()=>s,helper:o=>(s.helpers.add(o),o)}}function ia(e,t){for(let s=0;s<e.length;s++)uo(e[s],t)}function uo(e,t){switch(e.type){case 1:ia(e.cases,t),t.helper("plural");break;case 2:ia(e.items,t);break;case 6:{uo(e.key,t),t.helper("linked"),t.helper("type");break}case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function gu(e,t={}){const s=du(e);s.helper("normalize"),e.body&&uo(e.body,s);const n=s.context();e.helpers=Array.from(n.helpers)}function mu(e){const t=e.body;return t.type===2?oa(t):t.cases.forEach(s=>oa(s)),e}function oa(e){if(e.items.length===1){const t=e.items[0];(t.type===3||t.type===9)&&(e.static=t.value,delete t.value)}else{const t=[];for(let s=0;s<e.items.length;s++){const n=e.items[s];if(!(n.type===3||n.type===9)||n.value==null)break;t.push(n.value)}if(t.length===e.items.length){e.static=fo(t);for(let s=0;s<e.items.length;s++){const n=e.items[s];(n.type===3||n.type===9)&&delete n.value}}}}function Es(e){switch(e.t=e.type,e.type){case 0:{const t=e;Es(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,s=t.cases;for(let n=0;n<s.length;n++)Es(s[n]);t.c=s,delete t.cases;break}case 2:{const t=e,s=t.items;for(let n=0;n<s.length;n++)Es(s[n]);t.i=s,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;Es(t.key),t.k=t.key,delete t.key,t.modifier&&(Es(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function hu(e,t){const{sourceMap:s,filename:n,breakLineCode:i,needIndent:o}=t,a=t.location!==!1,l={filename:n,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:i,needIndent:o,indentLevel:0};a&&e.loc&&(l.source=e.loc.source);const r=()=>l;function u(U,y){l.code+=U}function f(U,y=!0){const w=y?i:"";u(o?w+"  ".repeat(U):w)}function d(U=!0){const y=++l.indentLevel;U&&f(y)}function h(U=!0){const y=--l.indentLevel;U&&f(y)}function T(){f(l.indentLevel)}return{context:r,push:u,indent:d,deindent:h,newline:T,helper:U=>`_${U}`,needIndent:()=>l.needIndent}}function yu(e,t){const{helper:s}=e;e.push(`${s("linked")}(`),Rs(e,t.key),t.modifier?(e.push(", "),Rs(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function vu(e,t){const{helper:s,needIndent:n}=e;e.push(`${s("normalize")}([`),e.indent(n());const i=t.items.length;for(let o=0;o<i&&(Rs(e,t.items[o]),o!==i-1);o++)e.push(", ");e.deindent(n()),e.push("])")}function _u(e,t){const{helper:s,needIndent:n}=e;if(t.cases.length>1){e.push(`${s("plural")}([`),e.indent(n());const i=t.cases.length;for(let o=0;o<i&&(Rs(e,t.cases[o]),o!==i-1);o++)e.push(", ");e.deindent(n()),e.push("])")}}function bu(e,t){t.body?Rs(e,t.body):e.push("null")}function Rs(e,t){const{helper:s}=e;switch(t.type){case 0:bu(e,t);break;case 1:_u(e,t);break;case 2:vu(e,t);break;case 6:yu(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${s("interpolate")}(${s("list")}(${t.index}))`,t);break;case 4:e.push(`${s("interpolate")}(${s("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break}}const Cu=(e,t={})=>{const s=K(t.mode)?t.mode:"normal",n=K(t.filename)?t.filename:"message.intl",i=!!t.sourceMap,o=t.breakLineCode!=null?t.breakLineCode:s==="arrow"?";":`
`,a=t.needIndent?t.needIndent:s!=="arrow",l=e.helpers||[],r=hu(e,{mode:s,filename:n,sourceMap:i,breakLineCode:o,needIndent:a});r.push(s==="normal"?"function __msg__ (ctx) {":"(ctx) => {"),r.indent(a),l.length>0&&(r.push(`const { ${fo(l.map(d=>`${d}: _${d}`),", ")} } = ctx`),r.newline()),r.push("return "),Rs(r,e),r.deindent(a),r.push("}"),delete e.helpers;const{code:u,map:f}=r.context();return{ast:e,code:u,map:f?f.toJSON():void 0}};function Su(e,t={}){const s=He({},t),n=!!s.jit,i=!!s.minify,o=s.optimize==null?!0:s.optimize,l=pu(s).parse(e);return n?(o&&mu(l),i&&Es(l),{ast:l,code:""}):(gu(l,s),Cu(l,s))}/*!
  * core-base v11.0.0-rc.1
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */function Tu(){typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(fs().__INTLIFY_PROD_DEVTOOLS__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(fs().__INTLIFY_DROP_MESSAGE_COMPILER__=!1)}function yi(e){return s=>wu(s,e)}function wu(e,t){const s=zu(t);if(s==null)throw cn(0);if(po(s)===1){const o=Iu(s);return e.plural(o.reduce((a,l)=>[...a,aa(e,l)],[]))}else return aa(e,s)}const Eu=["b","body"];function zu(e){return ns(e,Eu)}const Pu=["c","cases"];function Iu(e){return ns(e,Pu,[])}function aa(e,t){const s=xu(t);if(s!=null)return e.type==="text"?s:e.normalize([s]);{const n=Au(t).reduce((i,o)=>[...i,Ni(e,o)],[]);return e.normalize(n)}}const Lu=["s","static"];function xu(e){return ns(e,Lu)}const Mu=["i","items"];function Au(e){return ns(e,Mu,[])}function Ni(e,t){const s=po(t);switch(s){case 3:return In(t,s);case 9:return In(t,s);case 4:{const n=t;if(St(n,"k")&&n.k)return e.interpolate(e.named(n.k));if(St(n,"key")&&n.key)return e.interpolate(e.named(n.key));throw cn(s)}case 5:{const n=t;if(St(n,"i")&&We(n.i))return e.interpolate(e.list(n.i));if(St(n,"index")&&We(n.index))return e.interpolate(e.list(n.index));throw cn(s)}case 6:{const n=t,i=Ru(n),o=ku(n);return e.linked(Ni(e,o),i?Ni(e,i):void 0,e.type)}case 7:return In(t,s);case 8:return In(t,s);default:throw new Error(`unhandled node on format message part: ${s}`)}}const Nu=["t","type"];function po(e){return ns(e,Nu)}const Du=["v","value"];function In(e,t){const s=ns(e,Du);if(s)return s;throw cn(t)}const Ou=["m","modifier"];function Ru(e){return ns(e,Ou)}const Fu=["k","key"];function ku(e){const t=ns(e,Fu);if(t)return t;throw cn(6)}function ns(e,t,s){for(let n=0;n<t.length;n++){const i=t[n];if(St(e,i)&&e[i]!=null)return e[i]}return s}function cn(e){return new Error(`unhandled node type: ${e}`)}const Uu=e=>e;let Ln=we();function Fs(e){return ye(e)&&po(e)===0&&(St(e,"b")||St(e,"body"))}function Wu(e,t={}){let s=!1;const n=t.onError||tu;return t.onError=i=>{s=!0,n(i)},{...Su(e,t),detectError:s}}function $u(e,t){if(!__INTLIFY_DROP_MESSAGE_COMPILER__&&K(e)){he(t.warnHtmlMessage)&&t.warnHtmlMessage;const n=(t.onCacheKey||Uu)(e),i=Ln[n];if(i)return i;const{ast:o,detectError:a}=Wu(e,{...t,location:!1,jit:!0}),l=yi(o);return a?l:Ln[n]=l}else{const s=e.cacheKey;if(s){const n=Ln[s];return n||(Ln[s]=yi(e))}else return yi(e)}}let fn=null;function Vu(e){fn=e}function Hu(e,t,s){fn&&fn.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:s})}const ju=Yu("function:translate");function Yu(e){return t=>fn&&fn.emit(e,t)}const $t={INVALID_ARGUMENT:eu,INVALID_DATE_ARGUMENT:18,INVALID_ISO_DATE_ARGUMENT:19,NOT_SUPPORT_NON_STRING_MESSAGE:20,NOT_SUPPORT_LOCALE_PROMISE_VALUE:21,NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:22,NOT_SUPPORT_LOCALE_TYPE:23},Bu=24;function Vt(e){return ti(e,null,void 0)}function go(e,t){return t.locale!=null?la(t.locale):la(e.locale)}let vi;function la(e){if(K(e))return e;if(Ne(e)){if(e.resolvedOnce&&vi!=null)return vi;if(e.constructor.name==="Function"){const t=e();if(Jf(t))throw Vt($t.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return vi=t}else throw Vt($t.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}else throw Vt($t.NOT_SUPPORT_LOCALE_TYPE)}function Xu(e,t,s){return[...new Set([s,...$e(t)?t:ye(t)?Object.keys(t):K(t)?[t]:[s]])]}function Hl(e,t,s){const n=K(s)?s:un,i=e;i.__localeChainCache||(i.__localeChainCache=new Map);let o=i.__localeChainCache.get(n);if(!o){o=[];let a=[s];for(;$e(a);)a=ra(o,a,t);const l=$e(t)||!ue(t)?t:t.default?t.default:null;a=K(l)?[l]:l,$e(a)&&ra(o,a,!1),i.__localeChainCache.set(n,o)}return o}function ra(e,t,s){let n=!0;for(let i=0;i<t.length&&he(n);i++){const o=t[i];K(o)&&(n=Gu(e,t[i],s))}return n}function Gu(e,t,s){let n;const i=t.split("-");do{const o=i.join("-");n=Ku(e,o,s),i.splice(-1,1)}while(i.length&&n===!0);return n}function Ku(e,t,s){let n=!1;if(!e.includes(t)&&(n=!0,t)){n=t[t.length-1]!=="!";const i=t.replace(/!/g,"");e.push(i),($e(s)||ue(s))&&s[i]&&(n=s[i])}return n}const is=[];is[0]={w:[0],i:[3,0],"[":[4],o:[7]};is[1]={w:[1],".":[2],"[":[4],o:[7]};is[2]={w:[2],i:[3,0],0:[3,0]};is[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]};is[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]};is[5]={"'":[4,0],o:8,l:[5,0]};is[6]={'"':[4,0],o:8,l:[6,0]};const Ju=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function Qu(e){return Ju.test(e)}function qu(e){const t=e.charCodeAt(0),s=e.charCodeAt(e.length-1);return t===s&&(t===34||t===39)?e.slice(1,-1):e}function Zu(e){if(e==null)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function e0(e){const t=e.trim();return e.charAt(0)==="0"&&isNaN(parseInt(e))?!1:Qu(t)?qu(t):"*"+t}function t0(e){const t=[];let s=-1,n=0,i=0,o,a,l,r,u,f,d;const h=[];h[0]=()=>{a===void 0?a=l:a+=l},h[1]=()=>{a!==void 0&&(t.push(a),a=void 0)},h[2]=()=>{h[0](),i++},h[3]=()=>{if(i>0)i--,n=4,h[0]();else{if(i=0,a===void 0||(a=e0(a),a===!1))return!1;h[1]()}};function T(){const L=e[s+1];if(n===5&&L==="'"||n===6&&L==='"')return s++,l="\\"+L,h[0](),!0}for(;n!==null;)if(s++,o=e[s],!(o==="\\"&&T())){if(r=Zu(o),d=is[n],u=d[r]||d.l||8,u===8||(n=u[0],u[1]!==void 0&&(f=h[u[1]],f&&(l=o,f()===!1))))return;if(n===7)return t}}const ca=new Map;function s0(e,t){return ye(e)?e[t]:null}function n0(e,t){if(!ye(e))return null;let s=ca.get(t);if(s||(s=t0(t),s&&ca.set(t,s)),!s)return null;const n=s.length;let i=e,o=0;for(;o<n;){const a=i[s[o]];if(a===void 0||Ne(i))return null;i=a,o++}return i}const i0="11.0.0-rc.1",si=-1,un="en-US",fa="",ua=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function o0(){return{upper:(e,t)=>t==="text"&&K(e)?e.toUpperCase():t==="vnode"&&ye(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>t==="text"&&K(e)?e.toLowerCase():t==="vnode"&&ye(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>t==="text"&&K(e)?ua(e):t==="vnode"&&ye(e)&&"__v_isVNode"in e?ua(e.children):e}}let jl;function a0(e){jl=e}let Yl;function l0(e){Yl=e}let Bl;function r0(e){Bl=e}let Xl=null;const c0=e=>{Xl=e},f0=()=>Xl;let Gl=null;const pa=e=>{Gl=e},u0=()=>Gl;let da=0;function p0(e={}){const t=Ne(e.onWarn)?e.onWarn:qf,s=K(e.version)?e.version:i0,n=K(e.locale)||Ne(e.locale)?e.locale:un,i=Ne(n)?un:n,o=$e(e.fallbackLocale)||ue(e.fallbackLocale)||K(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:i,a=ue(e.messages)?e.messages:_i(i),l=ue(e.datetimeFormats)?e.datetimeFormats:_i(i),r=ue(e.numberFormats)?e.numberFormats:_i(i),u=He(we(),e.modifiers,o0()),f=e.pluralRules||we(),d=Ne(e.missing)?e.missing:null,h=he(e.missingWarn)||Os(e.missingWarn)?e.missingWarn:!0,T=he(e.fallbackWarn)||Os(e.fallbackWarn)?e.fallbackWarn:!0,L=!!e.fallbackFormat,M=!!e.unresolving,U=Ne(e.postTranslation)?e.postTranslation:null,y=ue(e.processor)?e.processor:null,w=he(e.warnHtmlMessage)?e.warnHtmlMessage:!0,x=!!e.escapeParameter,b=Ne(e.messageCompiler)?e.messageCompiler:jl,D=Ne(e.messageResolver)?e.messageResolver:Yl||s0,$=Ne(e.localeFallbacker)?e.localeFallbacker:Bl||Xu,R=ye(e.fallbackContext)?e.fallbackContext:void 0,se=e,fe=ye(se.__datetimeFormatters)?se.__datetimeFormatters:new Map,ve=ye(se.__numberFormatters)?se.__numberFormatters:new Map,_e=ye(se.__meta)?se.__meta:{};da++;const ce={version:s,cid:da,locale:n,fallbackLocale:o,messages:a,modifiers:u,pluralRules:f,missing:d,missingWarn:h,fallbackWarn:T,fallbackFormat:L,unresolving:M,postTranslation:U,processor:y,warnHtmlMessage:w,escapeParameter:x,messageCompiler:b,messageResolver:D,localeFallbacker:$,fallbackContext:R,onWarn:t,__meta:_e};return ce.datetimeFormats=l,ce.numberFormats=r,ce.__datetimeFormatters=fe,ce.__numberFormatters=ve,__INTLIFY_PROD_DEVTOOLS__&&Hu(ce,s,_e),ce}const _i=e=>({[e]:we()});function mo(e,t,s,n,i){const{missing:o,onWarn:a}=e;if(o!==null){const l=o(e,s,t,i);return K(l)?l:t}else return t}function Xs(e,t,s){const n=e;n.__localeChainCache=new Map,e.localeFallbacker(e,s,t)}function d0(e,t){return e===t?!1:e.split("-")[0]===t.split("-")[0]}function g0(e,t){const s=t.indexOf(e);if(s===-1)return!1;for(let n=s+1;n<t.length;n++)if(d0(e,t[n]))return!0;return!1}function ga(e,...t){const{datetimeFormats:s,unresolving:n,fallbackLocale:i,onWarn:o,localeFallbacker:a}=e,{__datetimeFormatters:l}=e,[r,u,f,d]=Di(...t),h=he(f.missingWarn)?f.missingWarn:e.missingWarn;he(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn;const T=!!f.part,L=go(e,f),M=a(e,i,L);if(!K(r)||r==="")return new Intl.DateTimeFormat(L,d).format(u);let U={},y,w=null;const x="datetime format";for(let $=0;$<M.length&&(y=M[$],U=s[y]||{},w=U[r],!ue(w));$++)mo(e,r,y,h,x);if(!ue(w)||!K(y))return n?si:r;let b=`${y}__${r}`;ei(d)||(b=`${b}__${JSON.stringify(d)}`);let D=l.get(b);return D||(D=new Intl.DateTimeFormat(y,He({},w,d)),l.set(b,D)),T?D.formatToParts(u):D.format(u)}const Kl=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Di(...e){const[t,s,n,i]=e,o=we();let a=we(),l;if(K(t)){const r=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!r)throw Vt($t.INVALID_ISO_DATE_ARGUMENT);const u=r[3]?r[3].trim().startsWith("T")?`${r[1].trim()}${r[3].trim()}`:`${r[1].trim()}T${r[3].trim()}`:r[1].trim();l=new Date(u);try{l.toISOString()}catch{throw Vt($t.INVALID_ISO_DATE_ARGUMENT)}}else if(Xf(t)){if(isNaN(t.getTime()))throw Vt($t.INVALID_DATE_ARGUMENT);l=t}else if(We(t))l=t;else throw Vt($t.INVALID_ARGUMENT);return K(s)?o.key=s:ue(s)&&Object.keys(s).forEach(r=>{Kl.includes(r)?a[r]=s[r]:o[r]=s[r]}),K(n)?o.locale=n:ue(n)&&(a=n),ue(i)&&(a=i),[o.key||"",l,o,a]}function ma(e,t,s){const n=e;for(const i in s){const o=`${t}__${i}`;n.__datetimeFormatters.has(o)&&n.__datetimeFormatters.delete(o)}}function ha(e,...t){const{numberFormats:s,unresolving:n,fallbackLocale:i,onWarn:o,localeFallbacker:a}=e,{__numberFormatters:l}=e,[r,u,f,d]=Oi(...t),h=he(f.missingWarn)?f.missingWarn:e.missingWarn;he(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn;const T=!!f.part,L=go(e,f),M=a(e,i,L);if(!K(r)||r==="")return new Intl.NumberFormat(L,d).format(u);let U={},y,w=null;const x="number format";for(let $=0;$<M.length&&(y=M[$],U=s[y]||{},w=U[r],!ue(w));$++)mo(e,r,y,h,x);if(!ue(w)||!K(y))return n?si:r;let b=`${y}__${r}`;ei(d)||(b=`${b}__${JSON.stringify(d)}`);let D=l.get(b);return D||(D=new Intl.NumberFormat(y,He({},w,d)),l.set(b,D)),T?D.formatToParts(u):D.format(u)}const Jl=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function Oi(...e){const[t,s,n,i]=e,o=we();let a=we();if(!We(t))throw Vt($t.INVALID_ARGUMENT);const l=t;return K(s)?o.key=s:ue(s)&&Object.keys(s).forEach(r=>{Jl.includes(r)?a[r]=s[r]:o[r]=s[r]}),K(n)?o.locale=n:ue(n)&&(a=n),ue(i)&&(a=i),[o.key||"",l,o,a]}function ya(e,t,s){const n=e;for(const i in s){const o=`${t}__${i}`;n.__numberFormatters.has(o)&&n.__numberFormatters.delete(o)}}const m0=e=>e,h0=e=>"",y0="text",v0=e=>e.length===0?"":fo(e),_0=Qf;function va(e,t){return e=Math.abs(e),t===2?e?e>1?1:0:1:e?Math.min(e,2):0}function b0(e){const t=We(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(We(e.named.count)||We(e.named.n))?We(e.named.count)?e.named.count:We(e.named.n)?e.named.n:t:t}function C0(e,t){t.count||(t.count=e),t.n||(t.n=e)}function S0(e={}){const t=e.locale,s=b0(e),n=ye(e.pluralRules)&&K(t)&&Ne(e.pluralRules[t])?e.pluralRules[t]:va,i=ye(e.pluralRules)&&K(t)&&Ne(e.pluralRules[t])?va:void 0,o=y=>y[n(s,y.length,i)],a=e.list||[],l=y=>a[y],r=e.named||we();We(e.pluralIndex)&&C0(s,r);const u=y=>r[y];function f(y,w){const x=Ne(e.messages)?e.messages(y,!!w):ye(e.messages)?e.messages[y]:!1;return x||(e.parent?e.parent.message(y):h0)}const d=y=>e.modifiers?e.modifiers[y]:m0,h=ue(e.processor)&&Ne(e.processor.normalize)?e.processor.normalize:v0,T=ue(e.processor)&&Ne(e.processor.interpolate)?e.processor.interpolate:_0,L=ue(e.processor)&&K(e.processor.type)?e.processor.type:y0,U={list:l,named:u,plural:o,linked:(y,...w)=>{const[x,b]=w;let D="text",$="";w.length===1?ye(x)?($=x.modifier||$,D=x.type||D):K(x)&&($=x||$):w.length===2&&(K(x)&&($=x||$),K(b)&&(D=b||D));const R=f(y,!0)(U),se=D==="vnode"&&$e(R)&&$?R[0]:R;return $?d($)(se,D):se},message:f,type:L,interpolate:T,normalize:h,values:He(we(),a,r)};return U}const _a=()=>"",ht=e=>Ne(e);function ba(e,...t){const{fallbackFormat:s,postTranslation:n,unresolving:i,messageCompiler:o,fallbackLocale:a,messages:l}=e,[r,u]=Ri(...t),f=he(u.missingWarn)?u.missingWarn:e.missingWarn,d=he(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn,h=he(u.escapeParameter)?u.escapeParameter:e.escapeParameter,T=!!u.resolvedMessage,L=K(u.default)||he(u.default)?he(u.default)?o?r:()=>r:u.default:s?o?r:()=>r:null,M=s||L!=null&&(K(L)||Ne(L)),U=go(e,u);h&&T0(u);let[y,w,x]=T?[r,U,l[U]||we()]:Ql(e,r,U,a,d,f),b=y,D=r;if(!T&&!(K(b)||Fs(b)||ht(b))&&M&&(b=L,D=b),!T&&(!(K(b)||Fs(b)||ht(b))||!K(w)))return i?si:r;let $=!1;const R=()=>{$=!0},se=ht(b)?b:ql(e,r,w,b,D,R);if($)return b;const fe=z0(e,w,x,u),ve=S0(fe),_e=w0(e,se,ve),ce=n?n(_e,r):_e;if(__INTLIFY_PROD_DEVTOOLS__){const je={timestamp:Date.now(),key:K(r)?r:ht(b)?b.key:"",locale:w||(ht(b)?b.locale:""),format:K(b)?b:ht(b)?b.source:"",message:ce};je.meta=He({},e.__meta,f0()||{}),ju(je)}return ce}function T0(e){$e(e.list)?e.list=e.list.map(t=>K(t)?sa(t):t):ye(e.named)&&Object.keys(e.named).forEach(t=>{K(e.named[t])&&(e.named[t]=sa(e.named[t]))})}function Ql(e,t,s,n,i,o){const{messages:a,onWarn:l,messageResolver:r,localeFallbacker:u}=e,f=u(e,n,s);let d=we(),h,T=null;const L="translate";for(let M=0;M<f.length&&(h=f[M],d=a[h]||we(),(T=r(d,t))===null&&(T=d[t]),!(K(T)||Fs(T)||ht(T)));M++)if(!g0(h,f)){const U=mo(e,t,h,o,L);U!==t&&(T=U)}return[T,h,d]}function ql(e,t,s,n,i,o){const{messageCompiler:a,warnHtmlMessage:l}=e;if(ht(n)){const u=n;return u.locale=u.locale||s,u.key=u.key||t,u}if(a==null){const u=()=>n;return u.locale=s,u.key=t,u}const r=a(n,E0(e,s,i,n,l,o));return r.locale=s,r.key=t,r.source=n,r}function w0(e,t,s){return t(s)}function Ri(...e){const[t,s,n]=e,i=we();if(!K(t)&&!We(t)&&!ht(t)&&!Fs(t))throw Vt($t.INVALID_ARGUMENT);const o=We(t)?String(t):(ht(t),t);return We(s)?i.plural=s:K(s)?i.default=s:ue(s)&&!ei(s)?i.named=s:$e(s)&&(i.list=s),We(n)?i.plural=n:K(n)?i.default=n:ue(n)&&He(i,n),[o,i]}function E0(e,t,s,n,i,o){return{locale:t,key:s,warnHtmlMessage:i,onError:a=>{throw o&&o(a),a},onCacheKey:a=>Yf(t,s,a)}}function z0(e,t,s,n){const{modifiers:i,pluralRules:o,messageResolver:a,fallbackLocale:l,fallbackWarn:r,missingWarn:u,fallbackContext:f}=e,h={locale:t,modifiers:i,pluralRules:o,messages:(T,L)=>{let M=a(s,T);if(M==null&&(f||L)){const[,,U]=Ql(f||e,T,t,l,r,u);M=a(U,T)}if(K(M)||Fs(M)){let U=!1;const w=ql(e,T,t,M,T,()=>{U=!0});return U?_a:w}else return ht(M)?M:_a}};return e.processor&&(h.processor=e.processor),n.list&&(h.list=n.list),n.named&&(h.named=n.named),We(n.plural)&&(h.pluralIndex=n.plural),h}Tu();/*!
  * vue-i18n v11.0.0-rc.1
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */const P0="11.0.0-rc.1";function I0(){typeof __VUE_I18N_FULL_INSTALL__!="boolean"&&(fs().__VUE_I18N_FULL_INSTALL__=!0),typeof __VUE_I18N_LEGACY_API__!="boolean"&&(fs().__VUE_I18N_LEGACY_API__=!0),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(fs().__INTLIFY_DROP_MESSAGE_COMPILER__=!1),typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(fs().__INTLIFY_PROD_DEVTOOLS__=!1)}const at={UNEXPECTED_RETURN_TYPE:Bu,INVALID_ARGUMENT:25,MUST_BE_CALL_SETUP_TOP:26,NOT_INSTALLED:27,REQUIRED_VALUE:28,INVALID_VALUE:29,CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:30,NOT_INSTALLED_WITH_PROVIDE:31,UNEXPECTED_ERROR:32,NOT_COMPATIBLE_LEGACY_VUE_I18N:33,NOT_AVAILABLE_COMPOSITION_IN_LEGACY:34};function pt(e,...t){return ti(e,null,void 0)}const Fi=ss("__translateVNode"),ki=ss("__datetimeParts"),Ui=ss("__numberParts"),Zl=ss("__setPluralRules"),er=ss("__injectWithOption"),Wi=ss("__dispose");function pn(e){if(!ye(e))return e;for(const t in e)if(St(e,t))if(!t.includes("."))ye(e[t])&&pn(e[t]);else{const s=t.split("."),n=s.length-1;let i=e,o=!1;for(let a=0;a<n;a++){if(s[a]in i||(i[s[a]]=we()),!ye(i[s[a]])){o=!0;break}i=i[s[a]]}o||(i[s[n]]=e[t],delete e[t]),ye(i[s[n]])&&pn(i[s[n]])}return e}function ho(e,t){const{messages:s,__i18n:n,messageResolver:i,flatJson:o}=t,a=ue(s)?s:$e(n)?we():{[e]:we()};if($e(n)&&n.forEach(l=>{if("locale"in l&&"resource"in l){const{locale:r,resource:u}=l;r?(a[r]=a[r]||we(),Dn(u,a[r])):Dn(u,a)}else K(l)&&Dn(JSON.parse(l),a)}),i==null&&o)for(const l in a)St(a,l)&&pn(a[l]);return a}function tr(e){return e.type}function sr(e,t,s){let n=ye(t.messages)?t.messages:we();"__i18nGlobal"in s&&(n=ho(e.locale.value,{messages:n,__i18n:s.__i18nGlobal}));const i=Object.keys(n);i.length&&i.forEach(o=>{e.mergeLocaleMessage(o,n[o])});{if(ye(t.datetimeFormats)){const o=Object.keys(t.datetimeFormats);o.length&&o.forEach(a=>{e.mergeDateTimeFormat(a,t.datetimeFormats[a])})}if(ye(t.numberFormats)){const o=Object.keys(t.numberFormats);o.length&&o.forEach(a=>{e.mergeNumberFormat(a,t.numberFormats[a])})}}}function Ca(e){return Xe(hn,null,e,0)}const Sa="__INTLIFY_META__",Ta=()=>[],L0=()=>!1;let wa=0;function Ea(e){return(t,s,n,i)=>e(s,n,ln()||void 0,i)}const x0=()=>{const e=ln();let t=null;return e&&(t=tr(e)[Sa])?{[Sa]:t}:null};function yo(e={}){const{__root:t,__injectWithOption:s}=e,n=t===void 0,i=e.flatJson,o=Vn?V:ac;let a=he(e.inheritLocale)?e.inheritLocale:!0;const l=o(t&&a?t.locale.value:K(e.locale)?e.locale:un),r=o(t&&a?t.fallbackLocale.value:K(e.fallbackLocale)||$e(e.fallbackLocale)||ue(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:l.value),u=o(ho(l.value,e)),f=o(ue(e.datetimeFormats)?e.datetimeFormats:{[l.value]:{}}),d=o(ue(e.numberFormats)?e.numberFormats:{[l.value]:{}});let h=t?t.missingWarn:he(e.missingWarn)||Os(e.missingWarn)?e.missingWarn:!0,T=t?t.fallbackWarn:he(e.fallbackWarn)||Os(e.fallbackWarn)?e.fallbackWarn:!0,L=t?t.fallbackRoot:he(e.fallbackRoot)?e.fallbackRoot:!0,M=!!e.fallbackFormat,U=Ne(e.missing)?e.missing:null,y=Ne(e.missing)?Ea(e.missing):null,w=Ne(e.postTranslation)?e.postTranslation:null,x=t?t.warnHtmlMessage:he(e.warnHtmlMessage)?e.warnHtmlMessage:!0,b=!!e.escapeParameter;const D=t?t.modifiers:ue(e.modifiers)?e.modifiers:{};let $=e.pluralRules||t&&t.pluralRules,R;R=(()=>{n&&pa(null);const _={version:P0,locale:l.value,fallbackLocale:r.value,messages:u.value,modifiers:D,pluralRules:$,missing:y===null?void 0:y,missingWarn:h,fallbackWarn:T,fallbackFormat:M,unresolving:!0,postTranslation:w===null?void 0:w,warnHtmlMessage:x,escapeParameter:b,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};_.datetimeFormats=f.value,_.numberFormats=d.value,_.__datetimeFormatters=ue(R)?R.__datetimeFormatters:void 0,_.__numberFormatters=ue(R)?R.__numberFormatters:void 0;const z=p0(_);return n&&pa(z),z})(),Xs(R,l.value,r.value);function fe(){return[l.value,r.value,u.value,f.value,d.value]}const ve=ws({get:()=>l.value,set:_=>{l.value=_,R.locale=l.value}}),_e=ws({get:()=>r.value,set:_=>{r.value=_,R.fallbackLocale=r.value,Xs(R,l.value,_)}}),ce=ws(()=>u.value),je=ws(()=>f.value),Ze=ws(()=>d.value);function dt(){return Ne(w)?w:null}function xe(_){w=_,R.postTranslation=_}function ge(){return U}function re(_){_!==null&&(y=Ea(_)),U=_,R.missing=y}const Me=(_,z,j,Q,oe,ae)=>{fe();let Ee;try{__INTLIFY_PROD_DEVTOOLS__,n||(R.fallbackContext=t?u0():void 0),Ee=_(R)}finally{__INTLIFY_PROD_DEVTOOLS__,n||(R.fallbackContext=void 0)}if(j!=="translate exists"&&We(Ee)&&Ee===si||j==="translate exists"&&!Ee){const[Ae,Re]=z();return t&&L?Q(t):oe(Ae)}else{if(ae(Ee))return Ee;throw pt(at.UNEXPECTED_RETURN_TYPE)}};function et(..._){return Me(z=>Reflect.apply(ba,null,[z,..._]),()=>Ri(..._),"translate",z=>Reflect.apply(z.t,z,[..._]),z=>z,z=>K(z))}function Ye(..._){const[z,j,Q]=_;if(Q&&!ye(Q))throw pt(at.INVALID_ARGUMENT);return et(z,j,He({resolvedMessage:!0},Q||{}))}function q(..._){return Me(z=>Reflect.apply(ga,null,[z,..._]),()=>Di(..._),"datetime format",z=>Reflect.apply(z.d,z,[..._]),()=>fa,z=>K(z))}function G(..._){return Me(z=>Reflect.apply(ha,null,[z,..._]),()=>Oi(..._),"number format",z=>Reflect.apply(z.n,z,[..._]),()=>fa,z=>K(z))}function ee(_){return _.map(z=>K(z)||We(z)||he(z)?Ca(String(z)):z)}const pe={normalize:ee,interpolate:_=>_,type:"vnode"};function ne(..._){return Me(z=>{let j;const Q=z;try{Q.processor=pe,j=Reflect.apply(ba,null,[Q,..._])}finally{Q.processor=null}return j},()=>Ri(..._),"translate",z=>z[Fi](..._),z=>[Ca(z)],z=>$e(z))}function Oe(..._){return Me(z=>Reflect.apply(ha,null,[z,..._]),()=>Oi(..._),"number format",z=>z[Ui](..._),Ta,z=>K(z)||$e(z))}function Bt(..._){return Me(z=>Reflect.apply(ga,null,[z,..._]),()=>Di(..._),"datetime format",z=>z[ki](..._),Ta,z=>K(z)||$e(z))}function tt(_){$=_,R.pluralRules=$}function Mt(_,z){return Me(()=>{if(!_)return!1;const j=K(z)?z:l.value,Q=v(j),oe=R.messageResolver(Q,_);return Fs(oe)||ht(oe)||K(oe)},()=>[_],"translate exists",j=>Reflect.apply(j.te,j,[_,z]),L0,j=>he(j))}function gt(_){let z=null;const j=Hl(R,r.value,l.value);for(let Q=0;Q<j.length;Q++){const oe=u.value[j[Q]]||{},ae=R.messageResolver(oe,_);if(ae!=null){z=ae;break}}return z}function m(_){const z=gt(_);return z??(t?t.tm(_)||{}:{})}function v(_){return u.value[_]||{}}function P(_,z){if(i){const j={[_]:z};for(const Q in j)St(j,Q)&&pn(j[Q]);z=j[_]}u.value[_]=z,R.messages=u.value}function W(_,z){u.value[_]=u.value[_]||{};const j={[_]:z};if(i)for(const Q in j)St(j,Q)&&pn(j[Q]);z=j[_],Dn(z,u.value[_]),R.messages=u.value}function k(_){return f.value[_]||{}}function c(_,z){f.value[_]=z,R.datetimeFormats=f.value,ma(R,_,z)}function g(_,z){f.value[_]=He(f.value[_]||{},z),R.datetimeFormats=f.value,ma(R,_,z)}function S(_){return d.value[_]||{}}function I(_,z){d.value[_]=z,R.numberFormats=d.value,ya(R,_,z)}function N(_,z){d.value[_]=He(d.value[_]||{},z),R.numberFormats=d.value,ya(R,_,z)}wa++,t&&Vn&&(vt(t.locale,_=>{a&&(l.value=_,R.locale=_,Xs(R,l.value,r.value))}),vt(t.fallbackLocale,_=>{a&&(r.value=_,R.fallbackLocale=_,Xs(R,l.value,r.value))}));const H={id:wa,locale:ve,fallbackLocale:_e,get inheritLocale(){return a},set inheritLocale(_){a=_,_&&t&&(l.value=t.locale.value,r.value=t.fallbackLocale.value,Xs(R,l.value,r.value))},get availableLocales(){return Object.keys(u.value).sort()},messages:ce,get modifiers(){return D},get pluralRules(){return $||{}},get isGlobal(){return n},get missingWarn(){return h},set missingWarn(_){h=_,R.missingWarn=h},get fallbackWarn(){return T},set fallbackWarn(_){T=_,R.fallbackWarn=T},get fallbackRoot(){return L},set fallbackRoot(_){L=_},get fallbackFormat(){return M},set fallbackFormat(_){M=_,R.fallbackFormat=M},get warnHtmlMessage(){return x},set warnHtmlMessage(_){x=_,R.warnHtmlMessage=_},get escapeParameter(){return b},set escapeParameter(_){b=_,R.escapeParameter=_},t:et,getLocaleMessage:v,setLocaleMessage:P,mergeLocaleMessage:W,getPostTranslationHandler:dt,setPostTranslationHandler:xe,getMissingHandler:ge,setMissingHandler:re,[Zl]:tt};return H.datetimeFormats=je,H.numberFormats=Ze,H.rt=Ye,H.te=Mt,H.tm=m,H.d=q,H.n=G,H.getDateTimeFormat=k,H.setDateTimeFormat=c,H.mergeDateTimeFormat=g,H.getNumberFormat=S,H.setNumberFormat=I,H.mergeNumberFormat=N,H[er]=s,H[Fi]=ne,H[ki]=Bt,H[Ui]=Oe,H}function M0(e){const t=K(e.locale)?e.locale:un,s=K(e.fallbackLocale)||$e(e.fallbackLocale)||ue(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:t,n=Ne(e.missing)?e.missing:void 0,i=he(e.silentTranslationWarn)||Os(e.silentTranslationWarn)?!e.silentTranslationWarn:!0,o=he(e.silentFallbackWarn)||Os(e.silentFallbackWarn)?!e.silentFallbackWarn:!0,a=he(e.fallbackRoot)?e.fallbackRoot:!0,l=!!e.formatFallbackMessages,r=ue(e.modifiers)?e.modifiers:{},u=e.pluralizationRules,f=Ne(e.postTranslation)?e.postTranslation:void 0,d=K(e.warnHtmlInMessage)?e.warnHtmlInMessage!=="off":!0,h=!!e.escapeParameterHtml,T=he(e.sync)?e.sync:!0;let L=e.messages;if(ue(e.sharedMessages)){const D=e.sharedMessages;L=Object.keys(D).reduce((R,se)=>{const fe=R[se]||(R[se]={});return He(fe,D[se]),R},L||{})}const{__i18n:M,__root:U,__injectWithOption:y}=e,w=e.datetimeFormats,x=e.numberFormats,b=e.flatJson;return{locale:t,fallbackLocale:s,messages:L,flatJson:b,datetimeFormats:w,numberFormats:x,missing:n,missingWarn:i,fallbackWarn:o,fallbackRoot:a,fallbackFormat:l,modifiers:r,pluralRules:u,postTranslation:f,warnHtmlMessage:d,escapeParameter:h,messageResolver:e.messageResolver,inheritLocale:T,__i18n:M,__root:U,__injectWithOption:y}}function $i(e={}){const t=yo(M0(e)),{__extender:s}=e,n={id:t.id,get locale(){return t.locale.value},set locale(i){t.locale.value=i},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(i){t.fallbackLocale.value=i},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get missing(){return t.getMissingHandler()},set missing(i){t.setMissingHandler(i)},get silentTranslationWarn(){return he(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(i){t.missingWarn=he(i)?!i:i},get silentFallbackWarn(){return he(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(i){t.fallbackWarn=he(i)?!i:i},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(i){t.fallbackFormat=i},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(i){t.setPostTranslationHandler(i)},get sync(){return t.inheritLocale},set sync(i){t.inheritLocale=i},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(i){t.warnHtmlMessage=i!=="off"},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(i){t.escapeParameter=i},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...i){return Reflect.apply(t.t,t,[...i])},rt(...i){return Reflect.apply(t.rt,t,[...i])},te(i,o){return t.te(i,o)},tm(i){return t.tm(i)},getLocaleMessage(i){return t.getLocaleMessage(i)},setLocaleMessage(i,o){t.setLocaleMessage(i,o)},mergeLocaleMessage(i,o){t.mergeLocaleMessage(i,o)},d(...i){return Reflect.apply(t.d,t,[...i])},getDateTimeFormat(i){return t.getDateTimeFormat(i)},setDateTimeFormat(i,o){t.setDateTimeFormat(i,o)},mergeDateTimeFormat(i,o){t.mergeDateTimeFormat(i,o)},n(...i){return Reflect.apply(t.n,t,[...i])},getNumberFormat(i){return t.getNumberFormat(i)},setNumberFormat(i,o){t.setNumberFormat(i,o)},mergeNumberFormat(i,o){t.mergeNumberFormat(i,o)}};return n.__extender=s,n}function A0(e,t,s){return{beforeCreate(){const n=ln();if(!n)throw pt(at.UNEXPECTED_ERROR);const i=this.$options;if(i.i18n){const o=i.i18n;if(i.__i18n&&(o.__i18n=i.__i18n),o.__root=t,this===this.$root)this.$i18n=za(e,o);else{o.__injectWithOption=!0,o.__extender=s.__vueI18nExtend,this.$i18n=$i(o);const a=this.$i18n;a.__extender&&(a.__disposer=a.__extender(this.$i18n))}}else if(i.__i18n)if(this===this.$root)this.$i18n=za(e,i);else{this.$i18n=$i({__i18n:i.__i18n,__injectWithOption:!0,__extender:s.__vueI18nExtend,__root:t});const o=this.$i18n;o.__extender&&(o.__disposer=o.__extender(this.$i18n))}else this.$i18n=e;i.__i18nGlobal&&sr(t,i,i),this.$t=(...o)=>this.$i18n.t(...o),this.$rt=(...o)=>this.$i18n.rt(...o),this.$te=(o,a)=>this.$i18n.te(o,a),this.$d=(...o)=>this.$i18n.d(...o),this.$n=(...o)=>this.$i18n.n(...o),this.$tm=o=>this.$i18n.tm(o),s.__setInstance(n,this.$i18n)},mounted(){},unmounted(){const n=ln();if(!n)throw pt(at.UNEXPECTED_ERROR);const i=this.$i18n;delete this.$t,delete this.$rt,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,i.__disposer&&(i.__disposer(),delete i.__disposer,delete i.__extender),s.__deleteInstance(n),delete this.$i18n}}}function za(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[Zl](t.pluralizationRules||e.pluralizationRules);const s=ho(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(s).forEach(n=>e.mergeLocaleMessage(n,s[n])),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach(n=>e.mergeDateTimeFormat(n,t.datetimeFormats[n])),t.numberFormats&&Object.keys(t.numberFormats).forEach(n=>e.mergeNumberFormat(n,t.numberFormats[n])),e}const vo={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>e==="parent"||e==="global",default:"parent"},i18n:{type:Object}};function N0({slots:e},t){return t.length===1&&t[0]==="default"?(e.default?e.default():[]).reduce((n,i)=>[...n,...i.type===Fe?i.children:[i]],[]):t.reduce((s,n)=>{const i=e[n];return i&&(s[n]=i()),s},we())}function nr(){return Fe}const D0=hs({name:"i18n-t",props:He({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>We(e)||!isNaN(e)}},vo),setup(e,t){const{slots:s,attrs:n}=t,i=e.i18n||ys({useScope:e.scope,__useComponent:!0});return()=>{const o=Object.keys(s).filter(d=>d!=="_"),a=we();e.locale&&(a.locale=e.locale),e.plural!==void 0&&(a.plural=K(e.plural)?+e.plural:e.plural);const l=N0(t,o),r=i[Fi](e.keypath,l,a),u=He(we(),n),f=K(e.tag)||ye(e.tag)?e.tag:nr();return kl(f,u,r)}}}),Pa=D0;function O0(e){return $e(e)&&!K(e[0])}function ir(e,t,s,n){const{slots:i,attrs:o}=t;return()=>{const a={part:!0};let l=we();e.locale&&(a.locale=e.locale),K(e.format)?a.key=e.format:ye(e.format)&&(K(e.format.key)&&(a.key=e.format.key),l=Object.keys(e.format).reduce((h,T)=>s.includes(T)?He(we(),h,{[T]:e.format[T]}):h,we()));const r=n(e.value,a,l);let u=[a.key];$e(r)?u=r.map((h,T)=>{const L=i[h.type],M=L?L({[h.type]:h.value,index:T,parts:r}):[h.value];return O0(M)&&(M[0].key=`${h.type}-${T}`),M}):K(r)&&(u=[r]);const f=He(we(),o),d=K(e.tag)||ye(e.tag)?e.tag:nr();return kl(d,f,u)}}const R0=hs({name:"i18n-n",props:He({value:{type:Number,required:!0},format:{type:[String,Object]}},vo),setup(e,t){const s=e.i18n||ys({useScope:e.scope,__useComponent:!0});return ir(e,t,Jl,(...n)=>s[Ui](...n))}}),Ia=R0;function F0(e,t){const s=e;if(e.mode==="composition")return s.__getInstance(t)||e.global;{const n=s.__getInstance(t);return n!=null?n.__composer:e.global.__composer}}function k0(e){const t=a=>{const{instance:l,value:r}=a;if(!l||!l.$)throw pt(at.UNEXPECTED_ERROR);const u=F0(e,l.$),f=La(r);return[Reflect.apply(u.t,u,[...xa(f)]),u]};return{created:(a,l)=>{const[r,u]=t(l);Vn&&e.global===u&&(a.__i18nWatcher=vt(u.locale,()=>{l.instance&&l.instance.$forceUpdate()})),a.__composer=u,a.textContent=r},unmounted:a=>{Vn&&a.__i18nWatcher&&(a.__i18nWatcher(),a.__i18nWatcher=void 0,delete a.__i18nWatcher),a.__composer&&(a.__composer=void 0,delete a.__composer)},beforeUpdate:(a,{value:l})=>{if(a.__composer){const r=a.__composer,u=La(l);a.textContent=Reflect.apply(r.t,r,[...xa(u)])}},getSSRProps:a=>{const[l]=t(a);return{textContent:l}}}}function La(e){if(K(e))return{path:e};if(ue(e)){if(!("path"in e))throw pt(at.REQUIRED_VALUE,"path");return e}else throw pt(at.INVALID_VALUE)}function xa(e){const{path:t,locale:s,args:n,choice:i,plural:o}=e,a={},l=n||{};return K(s)&&(a.locale=s),We(i)&&(a.plural=i),We(o)&&(a.plural=o),[t,l,a]}function U0(e,t,...s){const n=ue(s[0])?s[0]:{};(he(n.globalInstall)?n.globalInstall:!0)&&([Pa.name,"I18nT"].forEach(o=>e.component(o,Pa)),[Ia.name,"I18nN"].forEach(o=>e.component(o,Ia)),[Aa.name,"I18nD"].forEach(o=>e.component(o,Aa))),e.directive("t",k0(t))}const W0=ss("global-vue-i18n");function $0(e={}){const t=__VUE_I18N_LEGACY_API__&&he(e.legacy)?e.legacy:__VUE_I18N_LEGACY_API__,s=he(e.globalInjection)?e.globalInjection:!0,n=new Map,[i,o]=V0(e,t),a=ss("");function l(d){return n.get(d)||null}function r(d,h){n.set(d,h)}function u(d){n.delete(d)}const f={get mode(){return __VUE_I18N_LEGACY_API__&&t?"legacy":"composition"},async install(d,...h){if(d.__VUE_I18N_SYMBOL__=a,d.provide(d.__VUE_I18N_SYMBOL__,f),ue(h[0])){const M=h[0];f.__composerExtend=M.__composerExtend,f.__vueI18nExtend=M.__vueI18nExtend}let T=null;!t&&s&&(T=J0(d,f.global)),__VUE_I18N_FULL_INSTALL__&&U0(d,f,...h),__VUE_I18N_LEGACY_API__&&t&&d.mixin(A0(o,o.__composer,f));const L=d.unmount;d.unmount=()=>{T&&T(),f.dispose(),L()}},get global(){return o},dispose(){i.stop()},__instances:n,__getInstance:l,__setInstance:r,__deleteInstance:u};return f}function ys(e={}){const t=ln();if(t==null)throw pt(at.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&t.appContext.app!=null&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw pt(at.NOT_INSTALLED);const s=H0(t),n=Y0(s),i=tr(t),o=j0(e,i);if(o==="global")return sr(n,e,i),n;if(o==="parent"){let r=B0(s,t,e.__useComponent);return r==null&&(r=n),r}const a=s;let l=a.__getInstance(t);if(l==null){const r=He({},e);"__i18n"in i&&(r.__i18n=i.__i18n),n&&(r.__root=n),l=yo(r),a.__composerExtend&&(l[Wi]=a.__composerExtend(l)),G0(a,t,l),a.__setInstance(t,l)}return l}function V0(e,t){const s=Or(),n=__VUE_I18N_LEGACY_API__&&t?s.run(()=>$i(e)):s.run(()=>yo(e));if(n==null)throw pt(at.UNEXPECTED_ERROR);return[s,n]}function H0(e){const t=Zs(e.isCE?W0:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw pt(e.isCE?at.NOT_INSTALLED_WITH_PROVIDE:at.UNEXPECTED_ERROR);return t}function j0(e,t){return ei(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function Y0(e){return e.mode==="composition"?e.global:e.global.__composer}function B0(e,t,s=!1){let n=null;const i=t.root;let o=X0(t,s);for(;o!=null;){const a=e;if(e.mode==="composition")n=a.__getInstance(o);else if(__VUE_I18N_LEGACY_API__){const l=a.__getInstance(o);l!=null&&(n=l.__composer,s&&n&&!n[er]&&(n=null))}if(n!=null||i===o)break;o=o.parent}return n}function X0(e,t=!1){return e==null?null:t&&e.vnode.ctx||e.parent}function G0(e,t,s){Kn(()=>{},t),Jn(()=>{const n=s;e.__deleteInstance(t);const i=n[Wi];i&&(i(),delete n[Wi])},t)}const K0=["locale","fallbackLocale","availableLocales"],Ma=["t","rt","d","n","tm","te"];function J0(e,t){const s=Object.create(null);return K0.forEach(i=>{const o=Object.getOwnPropertyDescriptor(t,i);if(!o)throw pt(at.UNEXPECTED_ERROR);const a=Ge(o.value)?{get(){return o.value.value},set(l){o.value.value=l}}:{get(){return o.get&&o.get()}};Object.defineProperty(s,i,a)}),e.config.globalProperties.$i18n=s,Ma.forEach(i=>{const o=Object.getOwnPropertyDescriptor(t,i);if(!o||!o.value)throw pt(at.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${i}`,o)}),()=>{delete e.config.globalProperties.$i18n,Ma.forEach(i=>{delete e.config.globalProperties[`$${i}`]})}}const Q0=hs({name:"i18n-d",props:He({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},vo),setup(e,t){const s=e.i18n||ys({useScope:e.scope,__useComponent:!0});return ir(e,t,Kl,(...n)=>s[ki](...n))}}),Aa=Q0;I0();a0($u);l0(n0);r0(Hl);if(__INTLIFY_PROD_DEVTOOLS__){const e=fs();e.__INTLIFY__=!0,Vu(e.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__)}function q0(e,t,s,n,i,o,a){e.beginPath(),e.ellipse(t,s,n,i,0,0,Math.PI*2),e.strokeStyle=a,e.lineWidth=o,e.stroke()}class Z0{constructor(t){J(this,"mmToPixel",10);this.mmToPixel=t}drawCircleList(t,s,n,i,o){s.forEach(a=>{a.drawInnerCircle&&this.drawCircle(t,n,i,o,a)})}drawCircle(t,s,n,i,o){const a=(o.innerCircleLineRadiusX-o.innerCircleLineWidth)/2,l=(o.innerCircleLineRadiusY-o.innerCircleLineWidth)/2;this.drawEllipse(t,s,n,a*this.mmToPixel,l*this.mmToPixel,o.innerCircleLineWidth*this.mmToPixel,i)}drawEllipse(t,s,n,i,o,a,l){t.beginPath(),t.ellipse(s,n,i,o,0,0,Math.PI*2),t.strokeStyle=l,t.lineWidth=a,t.stroke()}}class e1{constructor(t){J(this,"mmToPixel",10);J(this,"textPaths",[]);this.mmToPixel=t,this.textPaths=[]}getTextPaths(){return this.textPaths}clearTextPaths(){this.textPaths=[]}drawCompanyList(t,s,n,i,o,a,l){s.forEach(r=>{this.drawCompanyName(t,r,n,i,o,a,r.color||l)})}drawCompanyName(t,s,n,i,o,a,l){this.clearTextPaths();const r=s.fontHeight*this.mmToPixel,u=s.fontWeight||"normal";t.save(),t.font=`${u} ${r}px ${s.fontFamily}`,t.fillStyle=l,t.textAlign="center",t.textBaseline="bottom";const f=s.companyName.split(""),d=f.length,h=s.borderOffset*this.mmToPixel,T=Math.PI*(.5+d/(s.textDistributionFactor*4)),L=T/d,M=s.rotateDirection==="clockwise"?-1:1,U=(s.startAngle?s.startAngle:0)+(s.rotateDirection==="clockwise"?Math.PI-T/2:Math.PI+(Math.PI-T)/2);if(s.adjustEllipseText){let y=0;d%2!==0?y=d/2:y=(d+1)/2,f.forEach((w,x)=>{const b=y-x-1,$=Math.pow(b/y,2)*L*s.adjustEllipseTextFactor,R=x-y,se=R/Math.abs(R);let fe=U+M*L*(x+.5);fe+=$*se;const ve=n+Math.cos(fe)*(o-r-h),_e=i+Math.sin(fe)*(a-r-h);t.save(),t.translate(ve,_e),t.rotate(fe+(s.rotateDirection==="clockwise"?-Math.PI/2:Math.PI/2)),t.scale(s.compression,1);const ce=new Path2D;ce.rect(-r/2,-r,r,r),this.textPaths.push({text:w,path:ce,type:"company",bounds:{x:ve-r/2,y:_e-r,width:r,height:r}}),t.fillText(w,0,0),t.restore()})}else f.forEach((y,w)=>{const x=U+M*L*(w+.5),b=n+Math.cos(x)*(o-r-h),D=i+Math.sin(x)*(a-r-h);t.save(),t.translate(b,D),t.rotate(x+(s.rotateDirection==="clockwise"?-Math.PI/2:Math.PI/2)),t.scale(s.compression,1);const $=new Path2D;$.rect(-r/2,-r,r,r),this.textPaths.push({text:y,path:$,type:"company",bounds:{x:b-r/2,y:D-r,width:r,height:r}}),t.fillText(y,0,0),t.restore()});t.restore()}}class t1{constructor(t,s){J(this,"mmToPixel",10);J(this,"rulerSize",80);J(this,"drawPositionCrossLines",(t,s,n,i,o,a,l)=>{const r=t;if(!r)return;const u=r.getContext("2d");if(u&&(u.clearRect(0,0,r.width,r.height),u.beginPath(),u.strokeStyle=l,u.lineWidth=1,u.moveTo(n,a),u.lineTo(r.width,a),u.moveTo(o,i),u.lineTo(o,r.height),u.stroke(),s)){const f=s.getContext("2d");f&&f.drawImage(r,0,0)}});J(this,"drawCurrentPositionText",(t,s,n,i,o,a)=>{t.fillStyle="black",t.font="bold 12px Arial",t.textAlign="left",t.textBaseline="top";const l=s/i,r=n/i;t.fillText(`${l.toFixed(1)}mm, ${r.toFixed(1)}mm, scale: ${i.toFixed(2)}`,o+5,a+5)});this.mmToPixel=t,this.rulerSize=s}drawRuler(t,s,n,i,o,a){if(!s.showRuler)return;const l=1/this.mmToPixel;t.save(),t.fillStyle="lightgray",a?t.fillRect(0,0,i,o):t.fillRect(0,0,o,i),t.fillStyle="black",t.font="10px Arial",t.textAlign="center",t.textBaseline="top";const r=this.mmToPixel,u=Math.ceil((i-o)*l/n);for(let f=0;f<=u;f++){const d=f*r*n+o;f%5===0?(t.beginPath(),a?(t.moveTo(d,0),t.lineTo(d,o*.8)):(t.moveTo(0,d),t.lineTo(o*.8,d)),t.lineWidth=1,t.stroke(),t.save(),a?t.fillText(f.toString(),d,o*.8):(t.translate(o*.8,d),t.rotate(-Math.PI/2),t.fillText(f.toString(),0,0)),t.restore()):(t.beginPath(),a?(t.moveTo(d,0),t.lineTo(d,o*.6)):(t.moveTo(0,d),t.lineTo(o*.6,d)),t.lineWidth=.5,t.stroke())}t.restore()}showCrossDashLine(t,s,n,i,o,a,l){if(!s.showDashLine)return;t.save(),t.strokeStyle="#bbbbbb",t.lineWidth=1,t.setLineDash([5,5]);const r=this.mmToPixel*5;for(let u=this.rulerSize;u<a;u+=r*n)t.beginPath(),t.moveTo(u,o),t.lineTo(u,l),t.stroke();for(let u=this.rulerSize;u<l;u+=r*n)t.beginPath(),t.moveTo(i,u),t.lineTo(a,u),t.stroke();t.restore()}}class s1{constructor(t){J(this,"mmToPixel",10);this.mmToPixel=t}drawSecurityPattern(t,s,n,i,o,a,l){t.save(),t.strokeStyle="#FFFFFF",t.lineWidth=s.securityPatternWidth*this.mmToPixel,t.globalCompositeOperation="destination-out";const r=s.securityPatternAngleRange*Math.PI/180;if(l||s.securityPatternParams.length===0){s.securityPatternParams=[];for(let u=0;u<s.securityPatternCount;u++){const f=Math.random()*Math.PI*2,h=Math.atan2(a*Math.cos(f),o*Math.sin(f))+(Math.random()-.5)*r;s.securityPatternParams.push({angle:f,lineAngle:h})}}s.securityPatternParams.forEach(({angle:u,lineAngle:f})=>{const d=n+o*Math.cos(u),h=i+a*Math.sin(u),T=s.securityPatternLength*this.mmToPixel,L=d-T/2*Math.cos(f),M=h-T/2*Math.sin(f),U=d+T/2*Math.cos(f),y=h+T/2*Math.sin(f);t.beginPath(),t.moveTo(L,M),t.lineTo(U,y),t.stroke()}),t.restore()}}class Na{constructor(t){J(this,"mmToPixel",10);this.mmToPixel=t}async drawSVGContent(t,s,n,i,o=1){try{const a=10*this.mmToPixel,l=document.createElement("div");l.innerHTML=s;const r=l.querySelector("svg");if(!r)throw new Error("Invalid SVG content");r.hasAttribute("width")||r.setAttribute("width","100"),r.hasAttribute("height")||r.setAttribute("height","100");const u=parseFloat(r.getAttribute("width")||"100"),f=parseFloat(r.getAttribute("height")||"100"),d=new XMLSerializer().serializeToString(r),T=`data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(d)))}`,L=new Image;L.src=T,await new Promise((M,U)=>{L.onload=M,L.onerror=U}),t.save(),t.translate(n,i),t.scale(1,1),t.strokeStyle="blue",t.lineWidth=1,t.strokeRect(10,10,a,a),t.restore(),console.log("draw svg base64Url")}catch(a){console.error("Error drawing SVG:",a)}}async loadAndDrawSVG(t,s,n,i,o=1){try{const a=10*this.mmToPixel;console.log("draw test svg content",s,n,i,o),t.save(),t.translate(n,i),t.scale(1,1),t.strokeStyle="blue",t.lineWidth=1,t.strokeRect(10,10,a,a),t.restore()}catch(a){console.error("Error loading SVG:",a)}}async drawStarShape(t,s,n,i,o){try{if(s.svgPath.startsWith("<svg"))await this.drawSVGContent(t,s.svgPath,n,i+s.starPositionY*this.mmToPixel,s.starDiameter*this.mmToPixel/40);else if(s.svgPath.endsWith(".svg"))await this.loadAndDrawSVG(t,s.svgPath,n,i+s.starPositionY*this.mmToPixel,s.starDiameter*this.mmToPixel/40);else{t.save(),t.translate(n,i+s.starPositionY*this.mmToPixel),t.scale(s.starDiameter*this.mmToPixel/2,s.starDiameter*this.mmToPixel/2);const a=new Path2D(s.svgPath);t.fillStyle=o,t.fill(a),t.restore()}}catch(a){console.error("Error in drawStarShape:",a)}}}class n1{constructor(){J(this,"primaryColor","blue");J(this,"ruler",{showRuler:!0,showFullRuler:!0,showCrossLine:!0,showDashLine:!0,showSideRuler:!0,showCurrentPositionText:!0});J(this,"drawStar",{svgPath:"M 0 -1 L 0.588 0.809 L -0.951 -0.309 L 0.951 -0.309 L -0.588 0.809 Z",drawStar:!1,starDiameter:14,starPositionY:0,scaleToSmallStar:!1});J(this,"securityPattern",{openSecurityPattern:!0,securityPatternWidth:.15,securityPatternLength:3,securityPatternCount:5,securityPatternAngleRange:40,securityPatternParams:[]});J(this,"company",{companyName:"印章绘制有限责任公司",compression:1,borderOffset:1,textDistributionFactor:5,fontFamily:"SimSun",fontHeight:4.2,fontWeight:"normal",shape:"ellipse",adjustEllipseText:!1,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"counterclockwise"});J(this,"taxNumber",{code:"000000000000000000",compression:.7,fontHeight:3.7,fontFamily:"Arial",fontWidth:1.3,letterSpacing:8,positionY:0,totalWidth:26,fontWeight:"normal"});J(this,"stampCode",{code:"1234567890",compression:1,fontHeight:1.2,fontFamily:"Arial",borderOffset:1,fontWidth:1.2,textDistributionFactor:50,fontWeight:"normal"});J(this,"stampType",{stampType:"印章类型",fontHeight:4.6,fontFamily:"Arial",fontWidth:3,compression:.75,letterSpacing:0,positionY:-3,fontWeight:"normal",lineSpacing:2});J(this,"agingEffect",{applyAging:!1,agingIntensity:50,agingEffectParams:[]});J(this,"outBorder",{drawInnerCircle:!0,innerCircleLineWidth:1,innerCircleLineRadiusX:20,innerCircleLineRadiusY:15});J(this,"innerCircle",{drawInnerCircle:!0,innerCircleLineWidth:.5,innerCircleLineRadiusX:16,innerCircleLineRadiusY:12});J(this,"outThinCircle",{drawInnerCircle:!0,innerCircleLineWidth:.2,innerCircleLineRadiusX:36,innerCircleLineRadiusY:27});J(this,"roughEdge",{drawRoughEdge:!0,roughEdgeWidth:.2,roughEdgeHeight:5,roughEdgeParams:[],roughEdgeProbability:.3,roughEdgeShift:8,roughEdgePoints:360});J(this,"stampTypeList",[{stampType:"印章类型",fontHeight:4.6,fontFamily:"Arial",fontWidth:3,compression:.75,letterSpacing:0,positionY:-3,fontWeight:"normal",lineSpacing:2}]);J(this,"companyList",[{companyName:"绘制印章有限责任公司",compression:1,borderOffset:1,textDistributionFactor:3,fontFamily:"SimSun",fontHeight:4.2,fontWeight:"normal",shape:"ellipse",adjustEllipseText:!0,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"counterclockwise"}]);J(this,"innerCircleList",[]);J(this,"imageList",[]);J(this,"drawStampConfigs",{roughEdge:this.roughEdge,ruler:this.ruler,drawStar:this.drawStar,securityPattern:this.securityPattern,company:this.company,stampCode:this.stampCode,width:40,height:30,stampType:this.stampType,primaryColor:this.primaryColor,borderWidth:1,refreshSecurityPattern:!1,refreshOld:!1,taxNumber:this.taxNumber,agingEffect:this.agingEffect,innerCircle:this.innerCircle,outThinCircle:this.outThinCircle,openManualAging:!1,stampTypeList:this.stampTypeList,companyList:this.companyList,innerCircleList:this.innerCircleList,imageList:this.imageList,scale:1,offsetX:0,offsetY:0,mmToPixel:0,outBorder:this.outBorder})}initDrawStampConfigs(){return this.drawStampConfigs}}class i1{constructor(t,s){J(this,"imageCanvas");J(this,"imageCtx");this.imageCanvas=document.createElement("canvas"),this.imageCanvas.width=t,this.imageCanvas.height=s;const n=this.imageCanvas.getContext("2d");if(!n)throw new Error("Failed to get image canvas context");this.imageCtx=n}async drawImage(t,s,n,i,o){this.imageCtx.clearRect(0,0,this.imageCanvas.width,this.imageCanvas.height);const a=document.createElement("div");a.innerHTML=t;const l=a.querySelector("svg");if(!l)throw new Error("Invalid SVG content");l.hasAttribute("width")||l.setAttribute("width",i.toString()),l.hasAttribute("height")||l.setAttribute("height",o.toString());const r=new XMLSerializer().serializeToString(l),f=`data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(r)))}`,d=new Image;return await new Promise((h,T)=>{d.onload=h,d.onerror=T,d.src=f}),this.imageCtx.drawImage(d,s,n,i,o),this.imageCanvas}getCanvas(){return this.imageCanvas}clear(){this.imageCtx.clearRect(0,0,this.imageCanvas.width,this.imageCanvas.height)}}class o1{constructor(t){J(this,"mmToPixel",10);J(this,"textPaths",[]);this.mmToPixel=t}getTextPaths(){return this.textPaths}clearTextPaths(){this.textPaths=[]}drawCode(t,s,n,i,o,a,l){const r=s.fontHeight*this.mmToPixel,u=s.code,f=s.fontWeight||"normal";t.save(),t.font=`${f} ${r}px ${s.fontFamily}`,t.fillStyle=l,t.textAlign="center",t.textBaseline="middle";const d=u.split(""),h=d.length;if(h===1){const T=n,L=i+a-r-s.borderOffset*this.mmToPixel;t.save(),t.translate(T,L),t.scale(s.compression,1),t.fillText(u,0,0);const M=t.measureText(u);this.textPaths.push({text:u,type:"code",path:new Path2D,bounds:{x:T-M.width*s.compression/2,y:L-r/2,width:M.width*s.compression,height:r}}),t.restore()}else{const T=Math.PI*((1+h)/s.textDistributionFactor),L=Math.PI/2+T/2,M=T/(h-1);d.forEach((U,y)=>{const w=L-M*y,x=n+Math.cos(w)*(o-r/2-s.borderOffset*this.mmToPixel),b=i+Math.sin(w)*(a-r/2-s.borderOffset*this.mmToPixel);t.save(),t.translate(x,b),t.rotate(w-Math.PI/2),t.scale(s.compression,1),t.fillText(U,0,0);const D=t.measureText(U);this.textPaths.push({text:U,type:"code",path:new Path2D,bounds:{x:x-D.width*s.compression/2,y:b-r/2,width:D.width*s.compression,height:r}}),t.restore()})}t.restore()}}class a1{constructor(t){J(this,"mmToPixel",10);J(this,"textPaths",[]);this.mmToPixel=t}getTextPaths(){return this.textPaths}clearTextPaths(){this.textPaths=[]}drawStampTypeList(t,s,n,i,o,a){t.save(),s.forEach(l=>{this.drawStampType(t,l,n,i,o,a)}),t.restore()}drawStampType(t,s,n,i,o,a){const l=s.fontHeight*this.mmToPixel,r=s.letterSpacing,u=s.positionY,f=s.fontWeight||"normal",d=s.lineSpacing*this.mmToPixel;t.save(),t.font=`${f} ${l}px ${s.fontFamily}`,t.fillStyle=a,t.textAlign="center",t.textBaseline="middle";const h=s.stampType.split(`
`),T=h.length;h.forEach((L,M)=>{const U=L.split(""),y=U.map($=>t.measureText($).width),w=y.reduce(($,R)=>$+R,0)+(U.length-1)*r*this.mmToPixel,x=(M-(T-1)/2)*(l+d),b=i+o*.5+u*this.mmToPixel+x;t.save(),t.translate(n,b),t.scale(s.compression,1);let D=-w/2;U.forEach(($,R)=>{const se=D+y[R]/2;t.fillText($,se,0),this.textPaths.push({text:$,type:"stampType",path:new Path2D,bounds:{x:n+se*s.compression,y:b-l/2,width:y[R]*s.compression,height:l}}),D+=y[R]+r*this.mmToPixel}),t.restore()}),t.restore()}}class l1{constructor(t){J(this,"mmToPixel",10);J(this,"textPaths",[]);this.mmToPixel=t}getTextPaths(){return this.textPaths}clearTextPaths(){this.textPaths=[]}drawTaxNumber(t,s,n,i,o){const a=s.fontHeight*this.mmToPixel,l=s.totalWidth*this.mmToPixel,r=s.positionY*this.mmToPixel+.3,u=s.fontWeight||"normal",f=s.letterSpacing*this.mmToPixel;t.save(),t.font=`${u} ${a}px ${s.fontFamily}`,t.fillStyle=o,t.textAlign="center",t.textBaseline="middle";const d=s.code.split(""),h=d.length,L=(l*s.compression-(h-1)*f)/h,M=h*L+(h-1)*f,U=n-M/2+L/2,y=i+r;d.forEach((w,x)=>{const b=U+x*(L+f);t.save(),t.translate(b,y),t.scale(s.compression,1.35),t.fillText(w,0,0),this.textPaths.push({text:w,type:"taxNumber",path:new Path2D,bounds:{x:b-L*s.compression/2,y:y-a/2,width:Math.abs(L*s.compression),height:a*1.35}}),t.restore()}),t.restore()}}const Ss=8,Rt=8;class bi{constructor(t,s){J(this,"scale",1);J(this,"offsetX",0);J(this,"offsetY",0);J(this,"mmToPixel");J(this,"canvasCtx");J(this,"offscreenCanvas");J(this,"canvas");J(this,"stampOffsetX",0);J(this,"stampOffsetY",0);J(this,"drawStampConfigs");J(this,"imageCache",new Map);J(this,"drawCircleUtils");J(this,"drawSvgUtils");J(this,"drawCompanyUtils");J(this,"drawRulerUtils");J(this,"drawSecurityPatternUtils");J(this,"initDrawStampConfigsUtils");J(this,"imageCanvas");J(this,"drawCodeUtils");J(this,"drawStampTypeUtils");J(this,"drawTaxNumberUtils");J(this,"isDraggable",!0);J(this,"isDragging",!1);J(this,"dragStartPos",{x:0,y:0});J(this,"stampPosition",{x:0,y:0});J(this,"onMouseUp",()=>{this.isDragging=!1,this.refreshStamp(!1,!1)});J(this,"onCanvasClick",t=>{this.canvas});J(this,"onMouseLeave",t=>{this.isDragging=!1,this.refreshStamp()});J(this,"onMouseDown",t=>{this.isDragging=!0,this.dragStartPos={x:t.clientX-this.stampOffsetX*this.mmToPixel,y:t.clientY-this.stampOffsetY*this.mmToPixel}});J(this,"onMouseMove",t=>{if(!this.drawStampConfigs.openManualAging)if(this.isDragging){const s=(t.clientX-this.dragStartPos.x)/this.mmToPixel,n=(t.clientY-this.dragStartPos.y)/this.mmToPixel;this.stampOffsetX=Math.round(s*10)/10,this.stampOffsetY=Math.round(n*10)/10,this.refreshStamp()}else{const s=this.canvas.getBoundingClientRect(),n=t.clientX-s.left,i=t.clientY-s.top,o=Math.round((n-Ss*this.mmToPixel)/this.mmToPixel*10)/10,a=Math.round((i-Rt*this.mmToPixel)/this.mmToPixel*10)/10;this.refreshStamp(),this.drawStampConfigs.ruler.showCurrentPositionText&&this.drawRulerUtils.drawCurrentPositionText(this.canvasCtx,o,a,this.scale,Ss*this.mmToPixel,Rt*this.mmToPixel),this.drawStampConfigs.ruler.showCrossLine&&this.drawRulerUtils.drawPositionCrossLines(this.offscreenCanvas,this.canvas,Ss*this.mmToPixel,Rt*this.mmToPixel,n,i,this.drawStampConfigs.primaryColor)}});if(!t)throw new Error("Canvas is null");const n=t.getContext("2d");if(!n)throw new Error("Failed to get canvas context");this.initDrawStampConfigsUtils=new n1,this.drawStampConfigs=this.initDrawStampConfigsUtils.initDrawStampConfigs(),this.canvasCtx=n,this.mmToPixel=s,this.canvas=t,this.offscreenCanvas=document.createElement("canvas"),this.canvas&&this.offscreenCanvas&&(this.offscreenCanvas.width=t.width,this.offscreenCanvas.height=t.height),this.addCanvasListener(),this.drawCircleUtils=new Z0(this.mmToPixel),this.drawSvgUtils=new Na(this.mmToPixel),this.drawCompanyUtils=new e1(this.mmToPixel),this.drawRulerUtils=new t1(this.mmToPixel,Ss*this.mmToPixel),this.drawSecurityPatternUtils=new s1(this.mmToPixel),this.drawCodeUtils=new o1(s),this.drawSvgUtils=new Na(s),this.imageCanvas=new i1(t.width,t.height),this.drawStampTypeUtils=new a1(s),this.drawTaxNumberUtils=new l1(s)}getDrawConfigs(){return this.drawStampConfigs}addManualAgingEffect(t,s,n){console.log("手动做旧   1",t,s,this.drawStampConfigs.agingEffect.agingEffectParams);const i=1*this.mmToPixel,o=t-this.stampOffsetX*this.mmToPixel,a=s-this.stampOffsetY*this.mmToPixel;for(let l=0;l<10;l++)this.drawStampConfigs.agingEffect.agingEffectParams.push({x:o,y:a,noiseSize:Math.random()*3+1,noise:Math.random()*200*n,strongNoiseSize:Math.random()*5+2,strongNoise:Math.random()*250*n+5,fade:Math.random()*50*n,seed:Math.random()});this.refreshStamp(!1,!1),this.canvasCtx.save(),this.canvasCtx.globalCompositeOperation="destination-out",this.canvasCtx.beginPath(),this.canvasCtx.arc(t,s,i,0,Math.PI*2,!0),this.canvasCtx.fillStyle="rgba(255, 255, 255, 0.5)",this.canvasCtx.fill(),this.canvasCtx.restore()}setDrawConfigs(t){this.drawStampConfigs=t}addCanvasListener(){this.canvas.addEventListener("mousemove",t=>{if(this.drawStampConfigs.openManualAging&&t.buttons===1){const s=this.canvas.getBoundingClientRect(),n=t.clientX-s.left,i=t.clientY-s.top,o=this.drawStampConfigs.agingEffect.agingIntensity/100;this.addManualAgingEffect(n,i,o)}else this.isDraggable&&this.onMouseMove(t)}),this.canvas.addEventListener("mouseleave",t=>{this.onMouseLeave(t)}),this.canvas.addEventListener("mousedown",t=>{if(this.onMouseDown(t),this.drawStampConfigs.openManualAging){const s=this.canvas.getBoundingClientRect(),n=t.clientX-s.left,i=t.clientY-s.top,o=this.drawStampConfigs.agingEffect.agingIntensity/100;this.addManualAgingEffect(n,i,o)}}),this.canvas.addEventListener("mouseup",t=>{this.onMouseUp()}),this.canvas.addEventListener("click",t=>{this.onCanvasClick(t)}),this.canvas.addEventListener("wheel",t=>{if(t.ctrlKey){t.preventDefault();const s=t.deltaY>0?.9:1.1;this.zoomCanvas(t.offsetX,t.offsetY,s)}})}zoomCanvas(t,s,n){const i=this.scale;this.scale*=n,this.scale=Math.max(.1,Math.min(5,this.scale)),this.offsetX=t-(t-this.offsetX)*(this.scale/i),this.offsetY=s-(s-this.offsetY)*(this.scale/i),this.refreshStamp()}async drawImageList(t,s,n,i){for(const o of s)if(o.imageUrl){let a=this.imageCache.get(o.imageUrl);if(a)this.drawSingleImage(t,a,o,n,i);else try{const l=new Image;l.src=o.imageUrl,await new Promise((u,f)=>{l.onload=u,l.onerror=f});const r=await createImageBitmap(l);this.imageCache.set(o.imageUrl,r),this.drawSingleImage(t,r,o,n,i),requestAnimationFrame(()=>{this.refreshStamp()})}catch(l){console.error("Error loading or processing image:",l)}}}drawSingleImage(t,s,n,i,o){let a=n.imageWidth*this.mmToPixel,l=n.imageHeight*this.mmToPixel;if(n.keepAspectRatio){const f=Math.min(a/s.width,l/s.height);a=s.width*f,l=s.height*f}const r=i-a/2+n.positionX*this.mmToPixel,u=o-l/2+n.positionY*this.mmToPixel;t.save(),t.drawImage(s,r,u,a,l),t.restore()}async clearImageCache(){for(const t of this.imageCache.values())t.close();this.imageCache.clear()}drawStampType(t,s,n,i,o){const a=s.fontHeight*this.mmToPixel,l=s.letterSpacing,r=s.positionY,u=s.fontWeight||"normal",f=s.lineSpacing*this.mmToPixel;t.save(),t.font=`${u} ${a}px ${s.fontFamily}`,t.fillStyle=this.drawStampConfigs.primaryColor,t.textAlign="center",t.textBaseline="middle";const d=s.stampType.split(`
`),h=d.length;d.forEach((T,L)=>{const M=T.split(""),U=M.map(D=>t.measureText(D).width),y=U.reduce((D,$)=>D+$,0)+(M.length-1)*l*this.mmToPixel,w=(L-(h-1)/2)*(a+f),x=i+o*.5+r*this.mmToPixel+w;t.save(),t.translate(n,x);let b=-y/2;t.scale(s.compression,1),M.forEach((D,$)=>{t.fillText(D,b+U[$]/2,0),b+=U[$]+l*this.mmToPixel}),t.restore()}),t.restore()}drawStampTypeList(t,s,n,i,o){s.forEach(a=>{this.drawStampType(t,a,n,i,o)}),t.restore()}drawTaxNumber(t,s,n,i){const o=s.fontHeight*this.mmToPixel,a=s.totalWidth*this.mmToPixel,l=s.positionY*this.mmToPixel+.3,r=s.fontWeight||"normal";t.save(),t.font=`${r} ${o}px ${s.fontFamily}`,t.fillStyle=this.drawStampConfigs.primaryColor,t.textAlign="center",t.textBaseline="middle";const u=s.code.split(""),f=u.length,d=this.drawStampConfigs.taxNumber.letterSpacing*this.mmToPixel,T=(a*this.drawStampConfigs.taxNumber.compression-(f-1)*d)/f,L=f*T+(f-1)*d,M=n-L/2+T/2,U=i+l*this.mmToPixel;u.forEach((y,w)=>{const x=M+w*(T+d);t.save(),t.translate(x,U),t.scale(this.drawStampConfigs.taxNumber.compression,1.35),t.fillText(y,0,0),t.restore()}),t.restore()}addRoughEdge(t,s,n,i,o,a,l=!1){const r=a*this.drawStampConfigs.roughEdge.roughEdgeHeight*.01,u=this.drawStampConfigs.roughEdge.roughEdgePoints,f=this.drawStampConfigs.roughEdge.roughEdgeShift;if(t.save(),t.fillStyle="white",t.globalCompositeOperation="destination-out",l||this.drawStampConfigs.roughEdge.roughEdgeParams.length===0){this.drawStampConfigs.roughEdge.roughEdgeParams=[];for(let d=0;d<u;d++){const h=d/u*Math.PI*2,L=Math.random()>this.drawStampConfigs.roughEdge.roughEdgeProbability?Math.random()*r*Math.random()+this.drawStampConfigs.roughEdge.roughEdgeWidth:0;this.drawStampConfigs.roughEdge.roughEdgeParams.push({angle:h,size:L,offset:f,opacity:1})}}this.drawStampConfigs.roughEdge.roughEdgeParams.forEach(({angle:d,size:h})=>{const T=s+Math.cos(d)*(i+f),L=n+Math.sin(d)*(o+f);h>0&&(t.beginPath(),t.arc(T,L,h*this.mmToPixel,0,Math.PI*2),t.fill())}),t.restore()}addAgingEffect(t,s,n,i=!1){if(!this.drawStampConfigs.agingEffect.applyAging)return;const o=t.getImageData(0,0,s,n),a=o.data,l=s/(2*this.scale)+this.stampOffsetX*this.mmToPixel/this.scale,r=n/(2*this.scale)+this.stampOffsetY*this.mmToPixel/this.scale,u=Math.max(s,n)/2*this.mmToPixel/this.scale;if(i||this.drawStampConfigs.agingEffect.agingEffectParams.length===0){this.drawStampConfigs.agingEffect.agingEffectParams=[];for(let f=0;f<n;f++)for(let d=0;d<s;d++){const h=(f*s+d)*4;if(Math.sqrt(Math.pow(d-l,2)+Math.pow(f-r,2))<=u&&a[h+3]>0){const L=this.drawStampConfigs.agingEffect.agingIntensity/100,M=Math.random();this.drawStampConfigs.agingEffect.agingEffectParams.push({x:d-this.stampOffsetX*this.mmToPixel,y:f-this.stampOffsetY*this.mmToPixel,noiseSize:Math.random()*3+1,noise:Math.random()*200*L,strongNoiseSize:Math.random()*5+2,strongNoise:Math.random()*250*L+5,fade:Math.random()*50*L,seed:M})}}}this.drawStampConfigs.agingEffect.agingEffectParams.forEach(f=>{const{x:d,y:h,noiseSize:T,noise:L,strongNoiseSize:M,strongNoise:U,fade:y,seed:w}=f,x=d+this.stampOffsetX*this.mmToPixel,b=h+this.stampOffsetY*this.mmToPixel,D=(Math.round(b)*s+Math.round(x))*4;w<.4&&this.addCircularNoise(a,s,x,b,T,L,!0),w<.05&&this.addCircularNoise(a,s,x,b,M,U,!0),w<.2&&(a[D+3]=Math.max(0,a[D+3]-y))}),t.putImageData(o,0,0)}addCircularNoise(t,s,n,i,o,a,l=!1){const r=o*o/4;for(let u=-o/2;u<o/2;u++)for(let f=-o/2;f<o/2;f++)if(f*f+u*u<=r){const d=Math.round(n+f),T=(Math.round(i+u)*s+d)*4;T>=0&&T<t.length&&(l?t[T+3]=Math.max(0,t[T+3]-a):(t[T]=Math.min(255,t[T]+a),t[T+1]=Math.min(255,t[T+1]+a),t[T+2]=Math.min(255,t[T+2]+a)))}}saveStampAsPNG(){let t=1,s=Math.max(this.drawStampConfigs.width,this.drawStampConfigs.height),n=(this.drawStampConfigs.width+t*2)*this.mmToPixel,i=(this.drawStampConfigs.height+t*2)*this.mmToPixel;(s+t)*this.mmToPixel,this.drawStampConfigs.ruler.showCrossLine=!1,this.drawStampConfigs.ruler.showRuler=!1,this.drawStampConfigs.ruler.showDashLine=!1,this.drawStampConfigs.ruler.showSideRuler=!1,this.drawStampConfigs.ruler.showFullRuler=!1,this.drawStampConfigs.ruler.showCurrentPositionText=!1,this.refreshStamp(),setTimeout(()=>{const o=document.createElement("canvas");o.width=n,o.height=i;const a=o.getContext("2d");if(!a)return;a.clearRect(0,0,n,i),a.drawImage(this.canvas,Ss*this.mmToPixel+this.stampOffsetX*this.mmToPixel,Rt*this.mmToPixel+this.stampOffsetY*this.mmToPixel,n,i,t*this.mmToPixel,t*this.mmToPixel,n,i),this.drawStampConfigs.agingEffect.applyAging&&this.addAgingEffect(a,n,i,!1);const l=o.toDataURL("image/png"),r=document.createElement("a");r.href=l,r.download="mystamp.png",document.body.appendChild(r),r.click(),document.body.removeChild(r),this.drawStampConfigs.ruler.showCrossLine=!0,this.drawStampConfigs.ruler.showRuler=!0,this.drawStampConfigs.ruler.showDashLine=!0,this.drawStampConfigs.ruler.showSideRuler=!0,this.drawStampConfigs.ruler.showFullRuler=!0,this.drawStampConfigs.ruler.showCurrentPositionText=!0,this.refreshStamp()},50)}refreshStamp(t=!1,s=!1,n=!1){this.canvasCtx.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvasCtx.save(),this.canvasCtx.translate(this.offsetX,this.offsetY),this.canvasCtx.scale(this.scale,this.scale);const i=this.drawStampConfigs.width/2*10+Ss*this.mmToPixel,o=this.drawStampConfigs.height/2*10+Rt*this.mmToPixel,a=i/this.scale,l=o/this.scale,r=this.mmToPixel,u=(this.drawStampConfigs.width-this.drawStampConfigs.outBorder.innerCircleLineWidth)/2,f=(this.drawStampConfigs.height-this.drawStampConfigs.outBorder.innerCircleLineWidth)/2,d=this.stampOffsetX*this.mmToPixel,h=this.stampOffsetY*this.mmToPixel,T=a+d,L=l+h;this.drawStamp(this.canvasCtx,T,L,u*r,f*r,this.drawStampConfigs.primaryColor,t,s,n),this.canvasCtx.restore(),this.drawStampConfigs.ruler.showRuler&&(this.drawStampConfigs.ruler.showSideRuler&&(this.drawRulerUtils.drawRuler(this.canvasCtx,this.drawStampConfigs.ruler,this.scale,this.canvas.width,Rt*this.mmToPixel,!0),this.drawRulerUtils.drawRuler(this.canvasCtx,this.drawStampConfigs.ruler,this.scale,this.canvas.height,Rt*this.mmToPixel,!1)),this.drawStampConfigs.ruler.showDashLine&&this.drawRulerUtils.showCrossDashLine(this.canvasCtx,this.drawStampConfigs.ruler,this.scale,Rt,Rt,this.canvas.width,this.canvas.height))}resetZoom(){this.scale=1,this.offsetX=0,this.offsetY=0,this.refreshStamp()}drawStamp(t,s,n,i,o,a,l=!1,r=!1,u=!1){t.clearRect(0,0,this.canvas.width,this.canvas.height);const f=this.offscreenCanvas;f.width=this.canvas.width,f.height=this.canvas.height;const d=f.getContext("2d");if(!d)return;const h=document.createElement("canvas");h.width=this.canvas.width,h.height=this.canvas.height,h.getContext("2d")&&(this.drawStampConfigs.outBorder.drawInnerCircle&&q0(d,s,n,i,o,this.drawStampConfigs.outBorder.innerCircleLineWidth*this.mmToPixel,a),d.save(),d.beginPath(),d.ellipse(s,n,i,o,0,0,Math.PI*2),d.clip(),this.drawStampConfigs.innerCircleList.length>0&&this.drawCircleUtils.drawCircleList(d,this.drawStampConfigs.innerCircleList,s,n,a),this.drawStampConfigs.drawStar.drawStar&&this.drawSvgUtils.drawStarShape(d,this.drawStampConfigs.drawStar,s,n,this.drawStampConfigs.primaryColor),this.drawStampConfigs.imageList&&this.drawStampConfigs.imageList.length>0&&this.drawImageList(d,this.drawStampConfigs.imageList,s,n),this.drawCompanyUtils.drawCompanyList(d,this.drawStampConfigs.companyList,s,n,i,o,this.drawStampConfigs.primaryColor),this.drawStampTypeUtils.drawStampTypeList(d,this.drawStampConfigs.stampTypeList,s,n,i,this.drawStampConfigs.primaryColor),this.drawCodeUtils.drawCode(d,this.drawStampConfigs.stampCode,s,n,i,o,this.drawStampConfigs.primaryColor),this.drawTaxNumberUtils.drawTaxNumber(d,this.drawStampConfigs.taxNumber,s,n,this.drawStampConfigs.primaryColor),d.restore(),t.save(),this.drawStampConfigs.roughEdge.drawRoughEdge&&this.addRoughEdge(d,s,n,i,o,this.drawStampConfigs.outBorder.innerCircleLineWidth*this.mmToPixel,u),this.drawStampConfigs.securityPattern.openSecurityPattern&&this.drawSecurityPatternUtils.drawSecurityPattern(d,this.drawStampConfigs.securityPattern,s,n,i,o,l),t.globalCompositeOperation="source-over",t.drawImage(f,0,0),t.restore(),this.drawStampConfigs.agingEffect.applyAging&&this.addAgingEffect(t,this.canvas.width,this.canvas.height,r))}setDraggable(t){this.isDraggable=t}}async function or(){try{if(window.queryLocalFonts){const e=await window.queryLocalFonts();return[...new Set(e.map(t=>t.family))]}else return["SimSun","SimHei","Microsoft YaHei","KaiTi","FangSong","STHeiti","STKaiti","STSong","STFangsong","LiSu","YouYuan","STZhongsong","STXihei","Arial","Times New Roman","Helvetica"]}catch(e){return console.error("获取系统字体失败:",e),["SimSun","SimHei","Microsoft YaHei","KaiTi"]}}const r1={drawRoughEdge:!1,roughEdgeWidth:.2,roughEdgeHeight:5,roughEdgeParams:[{angle:0,size:.5032563701178842,offset:8,opacity:1},{angle:.017453292519943295,size:.3379166289700789,offset:8,opacity:1},{angle:.03490658503988659,size:.49850828105362677,offset:8,opacity:1},{angle:.05235987755982988,size:0,offset:8,opacity:1},{angle:.06981317007977318,size:.33027000340902746,offset:8,opacity:1},{angle:.08726646259971647,size:0,offset:8,opacity:1},{angle:.10471975511965977,size:.47393696973712246,offset:8,opacity:1},{angle:.12217304763960307,size:.6350416726108645,offset:8,opacity:1},{angle:.13962634015954636,size:.42238834211115167,offset:8,opacity:1},{angle:.15707963267948966,size:.2486073483438663,offset:8,opacity:1},{angle:.17453292519943295,size:0,offset:8,opacity:1},{angle:.19198621771937624,size:.35123274555265016,offset:8,opacity:1},{angle:.20943951023931953,size:.22448742164649077,offset:8,opacity:1},{angle:.22689280275926282,size:.4586736104699405,offset:8,opacity:1},{angle:.24434609527920614,size:.3043430828713793,offset:8,opacity:1},{angle:.2617993877991494,size:.3678224039628227,offset:8,opacity:1},{angle:.2792526803190927,size:.6163525747317525,offset:8,opacity:1},{angle:.296705972839036,size:.4363211517667617,offset:8,opacity:1},{angle:.3141592653589793,size:.205723548131973,offset:8,opacity:1},{angle:.33161255787892263,size:.4441622976512367,offset:8,opacity:1},{angle:.3490658503988659,size:0,offset:8,opacity:1},{angle:.3665191429188092,size:0,offset:8,opacity:1},{angle:.3839724354387525,size:.43017918657846765,offset:8,opacity:1},{angle:.40142572795869574,size:.4300022134137814,offset:8,opacity:1},{angle:.41887902047863906,size:0,offset:8,opacity:1},{angle:.4363323129985824,size:.25786098537584295,offset:8,opacity:1},{angle:.45378560551852565,size:.26525313850692,offset:8,opacity:1},{angle:.47123889803846897,size:.2838264875019364,offset:8,opacity:1},{angle:.4886921905584123,size:.4487316582245055,offset:8,opacity:1},{angle:.5061454830783556,size:.22112605086657722,offset:8,opacity:1},{angle:.5235987755982988,size:.4294648804576925,offset:8,opacity:1},{angle:.5410520681182421,size:.2181025744046031,offset:8,opacity:1},{angle:.5585053606381855,size:.20053237605486948,offset:8,opacity:1},{angle:.5759586531581287,size:0,offset:8,opacity:1},{angle:.593411945678072,size:0,offset:8,opacity:1},{angle:.6108652381980153,size:.20041438725089644,offset:8,opacity:1},{angle:.6283185307179586,size:.2609460635118062,offset:8,opacity:1},{angle:.6457718232379019,size:.39197138754198557,offset:8,opacity:1},{angle:.6632251157578453,size:.2605111967121892,offset:8,opacity:1},{angle:.6806784082777886,size:.20565487747739025,offset:8,opacity:1},{angle:.6981317007977318,size:.28654344221403566,offset:8,opacity:1},{angle:.7155849933176751,size:0,offset:8,opacity:1},{angle:.7330382858376184,size:.4584907994244955,offset:8,opacity:1},{angle:.7504915783575618,size:0,offset:8,opacity:1},{angle:.767944870877505,size:0,offset:8,opacity:1},{angle:.7853981633974483,size:0,offset:8,opacity:1},{angle:.8028514559173915,size:.32662457556616653,offset:8,opacity:1},{angle:.8203047484373349,size:.3119138674864993,offset:8,opacity:1},{angle:.8377580409572781,size:.4433990641657177,offset:8,opacity:1},{angle:.8552113334772213,size:.2123628886068149,offset:8,opacity:1},{angle:.8726646259971648,size:0,offset:8,opacity:1},{angle:.890117918517108,size:.34950148564089184,offset:8,opacity:1},{angle:.9075712110370513,size:0,offset:8,opacity:1},{angle:.9250245035569946,size:.20120500318879,offset:8,opacity:1},{angle:.9424777960769379,size:0,offset:8,opacity:1},{angle:.9599310885968813,size:.5188164082336375,offset:8,opacity:1},{angle:.9773843811168246,size:0,offset:8,opacity:1},{angle:.9948376736367678,size:.22172251375465574,offset:8,opacity:1},{angle:1.0122909661567112,size:.2432685121130981,offset:8,opacity:1},{angle:1.0297442586766543,size:.2320690651003732,offset:8,opacity:1},{angle:1.0471975511965976,size:.3907006251295331,offset:8,opacity:1},{angle:1.064650843716541,size:0,offset:8,opacity:1},{angle:1.0821041362364843,size:.2625156540222333,offset:8,opacity:1},{angle:1.0995574287564276,size:0,offset:8,opacity:1},{angle:1.117010721276371,size:.3145596222093862,offset:8,opacity:1},{angle:1.1344640137963142,size:.32936787676169677,offset:8,opacity:1},{angle:1.1519173063162573,size:.2857231314976785,offset:8,opacity:1},{angle:1.1693705988362009,size:.20120534399793935,offset:8,opacity:1},{angle:1.186823891356144,size:0,offset:8,opacity:1},{angle:1.2042771838760875,size:.23237394092882147,offset:8,opacity:1},{angle:1.2217304763960306,size:.28398641365511185,offset:8,opacity:1},{angle:1.239183768915974,size:.21502333555596864,offset:8,opacity:1},{angle:1.2566370614359172,size:.3499693244354978,offset:8,opacity:1},{angle:1.2740903539558606,size:0,offset:8,opacity:1},{angle:1.2915436464758039,size:.5105882933252887,offset:8,opacity:1},{angle:1.3089969389957472,size:.21784406246195218,offset:8,opacity:1},{angle:1.3264502315156905,size:.22514376311106876,offset:8,opacity:1},{angle:1.3439035240356336,size:.29704092249825453,offset:8,opacity:1},{angle:1.3613568165555772,size:.4521472353131752,offset:8,opacity:1},{angle:1.3788101090755203,size:.21507145749905754,offset:8,opacity:1},{angle:1.3962634015954636,size:.21682236241700453,offset:8,opacity:1},{angle:1.413716694115407,size:.22356961113236007,offset:8,opacity:1},{angle:1.4311699866353502,size:.22219417312865522,offset:8,opacity:1},{angle:1.4486232791552935,size:.2977119909206255,offset:8,opacity:1},{angle:1.4660765716752369,size:.38291243837511746,offset:8,opacity:1},{angle:1.48352986419518,size:.3116663219443704,offset:8,opacity:1},{angle:1.5009831567151235,size:0,offset:8,opacity:1},{angle:1.5184364492350666,size:.25492313554632756,offset:8,opacity:1},{angle:1.53588974175501,size:.2228509582782908,offset:8,opacity:1},{angle:1.5533430342749532,size:.35672171117898743,offset:8,opacity:1},{angle:1.5707963267948966,size:0,offset:8,opacity:1},{angle:1.5882496193148399,size:0,offset:8,opacity:1},{angle:1.605702911834783,size:.4388252813562349,offset:8,opacity:1},{angle:1.6231562043547265,size:.2273036372658915,offset:8,opacity:1},{angle:1.6406094968746698,size:.21718818137496743,offset:8,opacity:1},{angle:1.6580627893946132,size:.36941527530149404,offset:8,opacity:1},{angle:1.6755160819145563,size:.21623671572399,offset:8,opacity:1},{angle:1.6929693744344996,size:.636688937830729,offset:8,opacity:1},{angle:1.7104226669544427,size:0,offset:8,opacity:1},{angle:1.7278759594743864,size:0,offset:8,opacity:1},{angle:1.7453292519943295,size:.27728150462159734,offset:8,opacity:1},{angle:1.7627825445142729,size:0,offset:8,opacity:1},{angle:1.780235837034216,size:0,offset:8,opacity:1},{angle:1.7976891295541593,size:.23328034069543777,offset:8,opacity:1},{angle:1.8151424220741026,size:0,offset:8,opacity:1},{angle:1.8325957145940461,size:.2586898150005329,offset:8,opacity:1},{angle:1.8500490071139892,size:.4994559385312126,offset:8,opacity:1},{angle:1.8675022996339325,size:.26421680867532127,offset:8,opacity:1},{angle:1.8849555921538759,size:.3209021989338088,offset:8,opacity:1},{angle:1.902408884673819,size:0,offset:8,opacity:1},{angle:1.9198621771937625,size:0,offset:8,opacity:1},{angle:1.9373154697137058,size:.26002544806143374,offset:8,opacity:1},{angle:1.9547687622336491,size:.2924936993062236,offset:8,opacity:1},{angle:1.9722220547535922,size:.43140240061138796,offset:8,opacity:1},{angle:1.9896753472735356,size:.29591579647411836,offset:8,opacity:1},{angle:2.007128639793479,size:.46532747343985814,offset:8,opacity:1},{angle:2.0245819323134224,size:0,offset:8,opacity:1},{angle:2.0420352248333655,size:.37989836106928254,offset:8,opacity:1},{angle:2.0594885173533086,size:.43824671847111324,offset:8,opacity:1},{angle:2.076941809873252,size:.21491306461629336,offset:8,opacity:1},{angle:2.0943951023931953,size:.2576066045616476,offset:8,opacity:1},{angle:2.111848394913139,size:.20559969896825836,offset:8,opacity:1},{angle:2.129301687433082,size:.5452053035796387,offset:8,opacity:1},{angle:2.1467549799530254,size:.4317948579735969,offset:8,opacity:1},{angle:2.1642082724729685,size:.2926508010599716,offset:8,opacity:1},{angle:2.1816615649929116,size:.37646244630618103,offset:8,opacity:1},{angle:2.199114857512855,size:.5182160912889464,offset:8,opacity:1},{angle:2.2165681500327987,size:.5838728943805604,offset:8,opacity:1},{angle:2.234021442552742,size:.21844249399465382,offset:8,opacity:1},{angle:2.251474735072685,size:0,offset:8,opacity:1},{angle:2.2689280275926285,size:.3721009993624145,offset:8,opacity:1},{angle:2.2863813201125716,size:0,offset:8,opacity:1},{angle:2.3038346126325147,size:.24598938578156437,offset:8,opacity:1},{angle:2.321287905152458,size:.4507505938631045,offset:8,opacity:1},{angle:2.3387411976724017,size:.25469635650569583,offset:8,opacity:1},{angle:2.356194490192345,size:.30528741051655217,offset:8,opacity:1},{angle:2.373647782712288,size:.37088412070072785,offset:8,opacity:1},{angle:2.3911010752322315,size:.24486197147462863,offset:8,opacity:1},{angle:2.408554367752175,size:0,offset:8,opacity:1},{angle:2.426007660272118,size:.43989471208136854,offset:8,opacity:1},{angle:2.443460952792061,size:.33696542573155486,offset:8,opacity:1},{angle:2.4609142453120048,size:0,offset:8,opacity:1},{angle:2.478367537831948,size:0,offset:8,opacity:1},{angle:2.495820830351891,size:0,offset:8,opacity:1},{angle:2.5132741228718345,size:.2505063689411901,offset:8,opacity:1},{angle:2.530727415391778,size:.31438011396387455,offset:8,opacity:1},{angle:2.548180707911721,size:.34374426546984016,offset:8,opacity:1},{angle:2.5656340004316642,size:.2305610481543743,offset:8,opacity:1},{angle:2.5830872929516078,size:.37268657957858453,offset:8,opacity:1},{angle:2.600540585471551,size:.25219421624230426,offset:8,opacity:1},{angle:2.6179938779914944,size:0,offset:8,opacity:1},{angle:2.6354471705114375,size:.23021680363052838,offset:8,opacity:1},{angle:2.652900463031381,size:.3483359449322281,offset:8,opacity:1},{angle:2.670353755551324,size:.3251780474107786,offset:8,opacity:1},{angle:2.6878070480712672,size:0,offset:8,opacity:1},{angle:2.705260340591211,size:.3687577362310519,offset:8,opacity:1},{angle:2.7227136331111543,size:.5694461018098402,offset:8,opacity:1},{angle:2.7401669256310974,size:.22425733709526832,offset:8,opacity:1},{angle:2.7576202181510405,size:0,offset:8,opacity:1},{angle:2.7750735106709836,size:.32709123488963454,offset:8,opacity:1},{angle:2.792526803190927,size:.3643797544475289,offset:8,opacity:1},{angle:2.8099800957108707,size:.25481296814968835,offset:8,opacity:1},{angle:2.827433388230814,size:.20233815801319835,offset:8,opacity:1},{angle:2.844886680750757,size:.22289439004543232,offset:8,opacity:1},{angle:2.8623399732707004,size:0,offset:8,opacity:1},{angle:2.8797932657906435,size:0,offset:8,opacity:1},{angle:2.897246558310587,size:0,offset:8,opacity:1},{angle:2.91469985083053,size:.22662095056906018,offset:8,opacity:1},{angle:2.9321531433504737,size:.6177358599983805,offset:8,opacity:1},{angle:2.949606435870417,size:.5579600289881892,offset:8,opacity:1},{angle:2.96705972839036,size:.24919239443796898,offset:8,opacity:1},{angle:2.9845130209103035,size:0,offset:8,opacity:1},{angle:3.001966313430247,size:.20285345071151972,offset:8,opacity:1},{angle:3.01941960595019,size:.29633213437720063,offset:8,opacity:1},{angle:3.036872898470133,size:.31615740448077223,offset:8,opacity:1},{angle:3.0543261909900767,size:.4883995719883713,offset:8,opacity:1},{angle:3.07177948351002,size:.2500925025911332,offset:8,opacity:1},{angle:3.089232776029963,size:.262931178068741,offset:8,opacity:1},{angle:3.1066860685499065,size:.3135512137978654,offset:8,opacity:1},{angle:3.12413936106985,size:.31083588965839803,offset:8,opacity:1},{angle:3.141592653589793,size:0,offset:8,opacity:1},{angle:3.159045946109736,size:.24804439339468767,offset:8,opacity:1},{angle:3.1764992386296798,size:0,offset:8,opacity:1},{angle:3.193952531149623,size:.2571395452468249,offset:8,opacity:1},{angle:3.211405823669566,size:.6279202198461746,offset:8,opacity:1},{angle:3.2288591161895095,size:.24288586668685336,offset:8,opacity:1},{angle:3.246312408709453,size:.34718500726687895,offset:8,opacity:1},{angle:3.2637657012293966,size:.47061815108690846,offset:8,opacity:1},{angle:3.2812189937493397,size:0,offset:8,opacity:1},{angle:3.2986722862692828,size:.4866126208626991,offset:8,opacity:1},{angle:3.3161255787892263,size:0,offset:8,opacity:1},{angle:3.3335788713091694,size:0,offset:8,opacity:1},{angle:3.3510321638291125,size:.22976093136313686,offset:8,opacity:1},{angle:3.368485456349056,size:0,offset:8,opacity:1},{angle:3.385938748868999,size:0,offset:8,opacity:1},{angle:3.4033920413889422,size:.4397259724357342,offset:8,opacity:1},{angle:3.4208453339088853,size:.24667841492062575,offset:8,opacity:1},{angle:3.4382986264288293,size:.43391379529064145,offset:8,opacity:1},{angle:3.455751918948773,size:0,offset:8,opacity:1},{angle:3.473205211468716,size:0,offset:8,opacity:1},{angle:3.490658503988659,size:.21961175000891414,offset:8,opacity:1},{angle:3.5081117965086026,size:.36890033272657746,offset:8,opacity:1},{angle:3.5255650890285457,size:.4326945036689108,offset:8,opacity:1},{angle:3.543018381548489,size:.3268470087082487,offset:8,opacity:1},{angle:3.560471674068432,size:.20854219238334568,offset:8,opacity:1},{angle:3.5779249665883754,size:.2423254936922336,offset:8,opacity:1},{angle:3.5953782591083185,size:.4064744910955269,offset:8,opacity:1},{angle:3.6128315516282616,size:0,offset:8,opacity:1},{angle:3.630284844148205,size:.31287388031447483,offset:8,opacity:1},{angle:3.647738136668149,size:.3948160804791036,offset:8,opacity:1},{angle:3.6651914291880923,size:0,offset:8,opacity:1},{angle:3.6826447217080354,size:.2026956152601606,offset:8,opacity:1},{angle:3.7000980142279785,size:0,offset:8,opacity:1},{angle:3.717551306747922,size:.44054519647144813,offset:8,opacity:1},{angle:3.735004599267865,size:.4130009051490618,offset:8,opacity:1},{angle:3.752457891787808,size:0,offset:8,opacity:1},{angle:3.7699111843077517,size:.49311184417141246,offset:8,opacity:1},{angle:3.787364476827695,size:.45508968556466084,offset:8,opacity:1},{angle:3.804817769347638,size:0,offset:8,opacity:1},{angle:3.8222710618675815,size:.48005037780936805,offset:8,opacity:1},{angle:3.839724354387525,size:0,offset:8,opacity:1},{angle:3.8571776469074686,size:.4561164475672816,offset:8,opacity:1},{angle:3.8746309394274117,size:.3002000652009621,offset:8,opacity:1},{angle:3.8920842319473548,size:.3336541884527151,offset:8,opacity:1},{angle:3.9095375244672983,size:0,offset:8,opacity:1},{angle:3.9269908169872414,size:.4811384274609927,offset:8,opacity:1},{angle:3.9444441095071845,size:0,offset:8,opacity:1},{angle:3.961897402027128,size:.5102504861379599,offset:8,opacity:1},{angle:3.979350694547071,size:.23214101697543765,offset:8,opacity:1},{angle:3.9968039870670142,size:.29156434123379016,offset:8,opacity:1},{angle:4.014257279586958,size:0,offset:8,opacity:1},{angle:4.031710572106902,size:.20004717274584805,offset:8,opacity:1},{angle:4.049163864626845,size:.3614701974794731,offset:8,opacity:1},{angle:4.066617157146788,size:0,offset:8,opacity:1},{angle:4.084070449666731,size:.2219991701875027,offset:8,opacity:1},{angle:4.101523742186674,size:.22431067260162876,offset:8,opacity:1},{angle:4.118977034706617,size:.30926005945781443,offset:8,opacity:1},{angle:4.136430327226561,size:.2513871895964691,offset:8,opacity:1},{angle:4.153883619746504,size:.2659224601694766,offset:8,opacity:1},{angle:4.171336912266447,size:.2216658406470619,offset:8,opacity:1},{angle:4.1887902047863905,size:.4273566387084955,offset:8,opacity:1},{angle:4.206243497306334,size:.34036553217806864,offset:8,opacity:1},{angle:4.223696789826278,size:.32533678816516465,offset:8,opacity:1},{angle:4.241150082346221,size:.23594875228493112,offset:8,opacity:1},{angle:4.258603374866164,size:0,offset:8,opacity:1},{angle:4.276056667386108,size:0,offset:8,opacity:1},{angle:4.293509959906051,size:.31051254932537203,offset:8,opacity:1},{angle:4.310963252425994,size:0,offset:8,opacity:1},{angle:4.328416544945937,size:0,offset:8,opacity:1},{angle:4.34586983746588,size:0,offset:8,opacity:1},{angle:4.363323129985823,size:.41758554970783024,offset:8,opacity:1},{angle:4.380776422505767,size:0,offset:8,opacity:1},{angle:4.39822971502571,size:.5165181485571547,offset:8,opacity:1},{angle:4.4156830075456535,size:0,offset:8,opacity:1},{angle:4.4331363000655974,size:0,offset:8,opacity:1},{angle:4.4505895925855405,size:.2875659335408256,offset:8,opacity:1},{angle:4.468042885105484,size:.2630697344221547,offset:8,opacity:1},{angle:4.485496177625427,size:.3044067435482997,offset:8,opacity:1},{angle:4.50294947014537,size:.3224161157725142,offset:8,opacity:1},{angle:4.520402762665314,size:.29296045447988417,offset:8,opacity:1},{angle:4.537856055185257,size:0,offset:8,opacity:1},{angle:4.5553093477052,size:.5348621754537155,offset:8,opacity:1},{angle:4.572762640225143,size:.23724437888510921,offset:8,opacity:1},{angle:4.590215932745086,size:0,offset:8,opacity:1},{angle:4.607669225265029,size:.23471290119278718,offset:8,opacity:1},{angle:4.625122517784973,size:.23001169467314136,offset:8,opacity:1},{angle:4.642575810304916,size:.3381947981140539,offset:8,opacity:1},{angle:4.66002910282486,size:0,offset:8,opacity:1},{angle:4.6774823953448035,size:.2820194526396866,offset:8,opacity:1},{angle:4.694935687864747,size:.27589340223228825,offset:8,opacity:1},{angle:4.71238898038469,size:0,offset:8,opacity:1},{angle:4.729842272904633,size:0,offset:8,opacity:1},{angle:4.747295565424576,size:0,offset:8,opacity:1},{angle:4.764748857944519,size:0,offset:8,opacity:1},{angle:4.782202150464463,size:.6047061570324029,offset:8,opacity:1},{angle:4.799655442984406,size:0,offset:8,opacity:1},{angle:4.81710873550435,size:0,offset:8,opacity:1},{angle:4.834562028024293,size:.4385393337320528,offset:8,opacity:1},{angle:4.852015320544236,size:0,offset:8,opacity:1},{angle:4.869468613064179,size:.3738861085343654,offset:8,opacity:1},{angle:4.886921905584122,size:.2369083255375507,offset:8,opacity:1},{angle:4.9043751981040655,size:.20042730995532476,offset:8,opacity:1},{angle:4.9218284906240095,size:.27407857191507573,offset:8,opacity:1},{angle:4.939281783143953,size:.260307602595212,offset:8,opacity:1},{angle:4.956735075663896,size:.22693010922108042,offset:8,opacity:1},{angle:4.974188368183839,size:0,offset:8,opacity:1},{angle:4.991641660703782,size:.36404125447608293,offset:8,opacity:1},{angle:5.009094953223726,size:.36660145102645764,offset:8,opacity:1},{angle:5.026548245743669,size:.21844437325488894,offset:8,opacity:1},{angle:5.044001538263612,size:0,offset:8,opacity:1},{angle:5.061454830783556,size:0,offset:8,opacity:1},{angle:5.078908123303499,size:0,offset:8,opacity:1},{angle:5.096361415823442,size:.6203742746502853,offset:8,opacity:1},{angle:5.113814708343385,size:0,offset:8,opacity:1},{angle:5.1312680008633285,size:0,offset:8,opacity:1},{angle:5.148721293383272,size:0,offset:8,opacity:1},{angle:5.1661745859032155,size:0,offset:8,opacity:1},{angle:5.183627878423159,size:.4394142349769681,offset:8,opacity:1},{angle:5.201081170943102,size:.46186021808530575,offset:8,opacity:1},{angle:5.218534463463046,size:.29002729040435316,offset:8,opacity:1},{angle:5.235987755982989,size:0,offset:8,opacity:1},{angle:5.253441048502932,size:0,offset:8,opacity:1},{angle:5.270894341022875,size:0,offset:8,opacity:1},{angle:5.288347633542818,size:.2776981969296649,offset:8,opacity:1},{angle:5.305800926062762,size:0,offset:8,opacity:1},{angle:5.323254218582705,size:.3066397012962211,offset:8,opacity:1},{angle:5.340707511102648,size:.22441601610004672,offset:8,opacity:1},{angle:5.358160803622591,size:.25396857567416625,offset:8,opacity:1},{angle:5.3756140961425345,size:.5404051622973916,offset:8,opacity:1},{angle:5.393067388662478,size:.274204802103521,offset:8,opacity:1},{angle:5.410520681182422,size:.2565405657819229,offset:8,opacity:1},{angle:5.427973973702365,size:.3725761880437103,offset:8,opacity:1},{angle:5.445427266222309,size:0,offset:8,opacity:1},{angle:5.462880558742252,size:0,offset:8,opacity:1},{angle:5.480333851262195,size:.25731937569411467,offset:8,opacity:1},{angle:5.497787143782138,size:.3188635809253325,offset:8,opacity:1},{angle:5.515240436302081,size:.3356669541977725,offset:8,opacity:1},{angle:5.532693728822024,size:.20400657089323843,offset:8,opacity:1},{angle:5.550147021341967,size:0,offset:8,opacity:1},{angle:5.567600313861911,size:0,offset:8,opacity:1},{angle:5.585053606381854,size:0,offset:8,opacity:1},{angle:5.602506898901798,size:.3165635361425635,offset:8,opacity:1},{angle:5.619960191421741,size:.3235314722259551,offset:8,opacity:1},{angle:5.6374134839416845,size:.26584898777242194,offset:8,opacity:1},{angle:5.654866776461628,size:.21243062483790226,offset:8,opacity:1},{angle:5.672320068981571,size:.30724136900399,offset:8,opacity:1},{angle:5.689773361501514,size:.2501099291145934,offset:8,opacity:1},{angle:5.707226654021458,size:.39400265901275955,offset:8,opacity:1},{angle:5.724679946541401,size:.2093418399866516,offset:8,opacity:1},{angle:5.742133239061344,size:.32923152005471246,offset:8,opacity:1},{angle:5.759586531581287,size:0,offset:8,opacity:1},{angle:5.77703982410123,size:.56456531278707,offset:8,opacity:1},{angle:5.794493116621174,size:.20557753914211502,offset:8,opacity:1},{angle:5.811946409141117,size:.5684634070194285,offset:8,opacity:1},{angle:5.82939970166106,size:.20249056891565564,offset:8,opacity:1},{angle:5.846852994181004,size:.3190036119950446,offset:8,opacity:1},{angle:5.8643062867009474,size:0,offset:8,opacity:1},{angle:5.8817595792208905,size:.20533088638096786,offset:8,opacity:1},{angle:5.899212871740834,size:.3241677112796085,offset:8,opacity:1},{angle:5.916666164260777,size:.3624193559997502,offset:8,opacity:1},{angle:5.93411945678072,size:.40343678768322566,offset:8,opacity:1},{angle:5.951572749300664,size:.4188672613641715,offset:8,opacity:1},{angle:5.969026041820607,size:.2285210234832376,offset:8,opacity:1},{angle:5.98647933434055,size:0,offset:8,opacity:1},{angle:6.003932626860494,size:.48733967322430105,offset:8,opacity:1},{angle:6.021385919380437,size:.24186501745395364,offset:8,opacity:1},{angle:6.03883921190038,size:.4584926710071769,offset:8,opacity:1},{angle:6.056292504420323,size:.25479465338552776,offset:8,opacity:1},{angle:6.073745796940266,size:.2281956836229175,offset:8,opacity:1},{angle:6.09119908946021,size:.29373851820189983,offset:8,opacity:1},{angle:6.1086523819801535,size:.3027785858358294,offset:8,opacity:1},{angle:6.126105674500097,size:.39200941647226684,offset:8,opacity:1},{angle:6.14355896702004,size:.5167887646509625,offset:8,opacity:1},{angle:6.161012259539983,size:.48184476186013575,offset:8,opacity:1},{angle:6.178465552059926,size:.2170741818198825,offset:8,opacity:1},{angle:6.19591884457987,size:0,offset:8,opacity:1},{angle:6.213372137099813,size:.20735775148015026,offset:8,opacity:1},{angle:6.230825429619757,size:.38122080663892877,offset:8,opacity:1},{angle:6.2482787221397,size:.25023583869705845,offset:8,opacity:1},{angle:6.265732014659643,size:.2303276717210175,offset:8,opacity:1}],roughEdgeProbability:.3,roughEdgeShift:8,roughEdgePoints:360},c1={showRuler:!0,showFullRuler:!0,showCrossLine:!0,showDashLine:!0,showSideRuler:!0,showCurrentPositionText:!0},f1={svgPath:"M 0 -1 L 0.588 0.809 L -0.951 -0.309 L 0.951 -0.309 L -0.588 0.809 Z",drawStar:!1,starDiameter:14,starPositionY:0,scaleToSmallStar:!1},u1={openSecurityPattern:!0,securityPatternWidth:.15,securityPatternLength:3,securityPatternCount:5,securityPatternAngleRange:40,securityPatternParams:[{angle:1.409748801283011,lineAngle:.1920074644466812},{angle:5.32114078029858,lineAngle:2.6432851725092466},{angle:4.1098611216984136,lineAngle:-2.5979078269198292},{angle:4.994408472565594,lineAngle:2.8406667666143948},{angle:2.962601457171679,lineAngle:-1.5064351132749016}]},p1={companyName:"印章绘制有限责任公司",compression:1,borderOffset:1,textDistributionFactor:5,fontFamily:"Songti SC",fontHeight:4.2,fontWeight:400,shape:"ellipse",adjustEllipseText:!1,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"counterclockwise"},d1={code:"1234567890",compression:1,fontHeight:1.2,fontFamily:"Arial",borderOffset:1,fontWidth:1.2,textDistributionFactor:50,fontWeight:"normal"},g1=40,m1=40,h1={stampType:"印章类型",fontHeight:4.6,fontFamily:"Arial",fontWidth:3,compression:.75,letterSpacing:0,positionY:-3,fontWeight:"normal",lineSpacing:2},y1="blue",v1=1,_1=!1,b1=!1,C1={code:"000000000000000000",compression:.7,fontHeight:3.7,fontFamily:"Arial",fontWidth:1.3,letterSpacing:8,positionY:0,totalWidth:26,fontWeight:"normal"},S1={applyAging:!1,agingIntensity:50,agingEffectParams:[]},T1={drawInnerCircle:!0,innerCircleLineWidth:.5,innerCircleLineRadiusX:16,innerCircleLineRadiusY:12},w1={drawInnerCircle:!0,innerCircleLineWidth:.2,innerCircleLineRadiusX:36,innerCircleLineRadiusY:27},E1=!1,z1=[{stampType:"印章类型",fontHeight:4.6,fontFamily:"Arial",fontWidth:3,compression:.75,letterSpacing:0,positionY:-3,fontWeight:"normal",lineSpacing:2}],P1=[{companyName:"模板印章有限责任公司",compression:1,borderOffset:1,textDistributionFactor:3,fontFamily:"SimSun",fontHeight:4.2,fontWeight:"normal",shape:"ellipse",adjustEllipseText:!0,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"counterclockwise"}],I1=[],L1=[],x1=1,M1=0,A1=0,N1=0,D1={drawInnerCircle:!0,innerCircleLineWidth:1,innerCircleLineRadiusX:20,innerCircleLineRadiusY:15},O1={roughEdge:r1,ruler:c1,drawStar:f1,securityPattern:u1,company:p1,stampCode:d1,width:g1,height:m1,stampType:h1,primaryColor:y1,borderWidth:v1,refreshSecurityPattern:_1,refreshOld:b1,taxNumber:C1,agingEffect:S1,innerCircle:T1,outThinCircle:w1,openManualAging:E1,stampTypeList:z1,companyList:P1,innerCircleList:I1,imageList:L1,scale:x1,offsetX:M1,offsetY:A1,mmToPixel:N1,outBorder:D1},R1={drawRoughEdge:!1,roughEdgeWidth:.2,roughEdgeHeight:5,roughEdgeParams:[{angle:0,size:.5032563701178842,offset:8,opacity:1},{angle:.017453292519943295,size:.3379166289700789,offset:8,opacity:1},{angle:.03490658503988659,size:.49850828105362677,offset:8,opacity:1},{angle:.05235987755982988,size:0,offset:8,opacity:1},{angle:.06981317007977318,size:.33027000340902746,offset:8,opacity:1},{angle:.08726646259971647,size:0,offset:8,opacity:1},{angle:.10471975511965977,size:.47393696973712246,offset:8,opacity:1},{angle:.12217304763960307,size:.6350416726108645,offset:8,opacity:1},{angle:.13962634015954636,size:.42238834211115167,offset:8,opacity:1},{angle:.15707963267948966,size:.2486073483438663,offset:8,opacity:1},{angle:.17453292519943295,size:0,offset:8,opacity:1},{angle:.19198621771937624,size:.35123274555265016,offset:8,opacity:1},{angle:.20943951023931953,size:.22448742164649077,offset:8,opacity:1},{angle:.22689280275926282,size:.4586736104699405,offset:8,opacity:1},{angle:.24434609527920614,size:.3043430828713793,offset:8,opacity:1},{angle:.2617993877991494,size:.3678224039628227,offset:8,opacity:1},{angle:.2792526803190927,size:.6163525747317525,offset:8,opacity:1},{angle:.296705972839036,size:.4363211517667617,offset:8,opacity:1},{angle:.3141592653589793,size:.205723548131973,offset:8,opacity:1},{angle:.33161255787892263,size:.4441622976512367,offset:8,opacity:1},{angle:.3490658503988659,size:0,offset:8,opacity:1},{angle:.3665191429188092,size:0,offset:8,opacity:1},{angle:.3839724354387525,size:.43017918657846765,offset:8,opacity:1},{angle:.40142572795869574,size:.4300022134137814,offset:8,opacity:1},{angle:.41887902047863906,size:0,offset:8,opacity:1},{angle:.4363323129985824,size:.25786098537584295,offset:8,opacity:1},{angle:.45378560551852565,size:.26525313850692,offset:8,opacity:1},{angle:.47123889803846897,size:.2838264875019364,offset:8,opacity:1},{angle:.4886921905584123,size:.4487316582245055,offset:8,opacity:1},{angle:.5061454830783556,size:.22112605086657722,offset:8,opacity:1},{angle:.5235987755982988,size:.4294648804576925,offset:8,opacity:1},{angle:.5410520681182421,size:.2181025744046031,offset:8,opacity:1},{angle:.5585053606381855,size:.20053237605486948,offset:8,opacity:1},{angle:.5759586531581287,size:0,offset:8,opacity:1},{angle:.593411945678072,size:0,offset:8,opacity:1},{angle:.6108652381980153,size:.20041438725089644,offset:8,opacity:1},{angle:.6283185307179586,size:.2609460635118062,offset:8,opacity:1},{angle:.6457718232379019,size:.39197138754198557,offset:8,opacity:1},{angle:.6632251157578453,size:.2605111967121892,offset:8,opacity:1},{angle:.6806784082777886,size:.20565487747739025,offset:8,opacity:1},{angle:.6981317007977318,size:.28654344221403566,offset:8,opacity:1},{angle:.7155849933176751,size:0,offset:8,opacity:1},{angle:.7330382858376184,size:.4584907994244955,offset:8,opacity:1},{angle:.7504915783575618,size:0,offset:8,opacity:1},{angle:.767944870877505,size:0,offset:8,opacity:1},{angle:.7853981633974483,size:0,offset:8,opacity:1},{angle:.8028514559173915,size:.32662457556616653,offset:8,opacity:1},{angle:.8203047484373349,size:.3119138674864993,offset:8,opacity:1},{angle:.8377580409572781,size:.4433990641657177,offset:8,opacity:1},{angle:.8552113334772213,size:.2123628886068149,offset:8,opacity:1},{angle:.8726646259971648,size:0,offset:8,opacity:1},{angle:.890117918517108,size:.34950148564089184,offset:8,opacity:1},{angle:.9075712110370513,size:0,offset:8,opacity:1},{angle:.9250245035569946,size:.20120500318879,offset:8,opacity:1},{angle:.9424777960769379,size:0,offset:8,opacity:1},{angle:.9599310885968813,size:.5188164082336375,offset:8,opacity:1},{angle:.9773843811168246,size:0,offset:8,opacity:1},{angle:.9948376736367678,size:.22172251375465574,offset:8,opacity:1},{angle:1.0122909661567112,size:.2432685121130981,offset:8,opacity:1},{angle:1.0297442586766543,size:.2320690651003732,offset:8,opacity:1},{angle:1.0471975511965976,size:.3907006251295331,offset:8,opacity:1},{angle:1.064650843716541,size:0,offset:8,opacity:1},{angle:1.0821041362364843,size:.2625156540222333,offset:8,opacity:1},{angle:1.0995574287564276,size:0,offset:8,opacity:1},{angle:1.117010721276371,size:.3145596222093862,offset:8,opacity:1},{angle:1.1344640137963142,size:.32936787676169677,offset:8,opacity:1},{angle:1.1519173063162573,size:.2857231314976785,offset:8,opacity:1},{angle:1.1693705988362009,size:.20120534399793935,offset:8,opacity:1},{angle:1.186823891356144,size:0,offset:8,opacity:1},{angle:1.2042771838760875,size:.23237394092882147,offset:8,opacity:1},{angle:1.2217304763960306,size:.28398641365511185,offset:8,opacity:1},{angle:1.239183768915974,size:.21502333555596864,offset:8,opacity:1},{angle:1.2566370614359172,size:.3499693244354978,offset:8,opacity:1},{angle:1.2740903539558606,size:0,offset:8,opacity:1},{angle:1.2915436464758039,size:.5105882933252887,offset:8,opacity:1},{angle:1.3089969389957472,size:.21784406246195218,offset:8,opacity:1},{angle:1.3264502315156905,size:.22514376311106876,offset:8,opacity:1},{angle:1.3439035240356336,size:.29704092249825453,offset:8,opacity:1},{angle:1.3613568165555772,size:.4521472353131752,offset:8,opacity:1},{angle:1.3788101090755203,size:.21507145749905754,offset:8,opacity:1},{angle:1.3962634015954636,size:.21682236241700453,offset:8,opacity:1},{angle:1.413716694115407,size:.22356961113236007,offset:8,opacity:1},{angle:1.4311699866353502,size:.22219417312865522,offset:8,opacity:1},{angle:1.4486232791552935,size:.2977119909206255,offset:8,opacity:1},{angle:1.4660765716752369,size:.38291243837511746,offset:8,opacity:1},{angle:1.48352986419518,size:.3116663219443704,offset:8,opacity:1},{angle:1.5009831567151235,size:0,offset:8,opacity:1},{angle:1.5184364492350666,size:.25492313554632756,offset:8,opacity:1},{angle:1.53588974175501,size:.2228509582782908,offset:8,opacity:1},{angle:1.5533430342749532,size:.35672171117898743,offset:8,opacity:1},{angle:1.5707963267948966,size:0,offset:8,opacity:1},{angle:1.5882496193148399,size:0,offset:8,opacity:1},{angle:1.605702911834783,size:.4388252813562349,offset:8,opacity:1},{angle:1.6231562043547265,size:.2273036372658915,offset:8,opacity:1},{angle:1.6406094968746698,size:.21718818137496743,offset:8,opacity:1},{angle:1.6580627893946132,size:.36941527530149404,offset:8,opacity:1},{angle:1.6755160819145563,size:.21623671572399,offset:8,opacity:1},{angle:1.6929693744344996,size:.636688937830729,offset:8,opacity:1},{angle:1.7104226669544427,size:0,offset:8,opacity:1},{angle:1.7278759594743864,size:0,offset:8,opacity:1},{angle:1.7453292519943295,size:.27728150462159734,offset:8,opacity:1},{angle:1.7627825445142729,size:0,offset:8,opacity:1},{angle:1.780235837034216,size:0,offset:8,opacity:1},{angle:1.7976891295541593,size:.23328034069543777,offset:8,opacity:1},{angle:1.8151424220741026,size:0,offset:8,opacity:1},{angle:1.8325957145940461,size:.2586898150005329,offset:8,opacity:1},{angle:1.8500490071139892,size:.4994559385312126,offset:8,opacity:1},{angle:1.8675022996339325,size:.26421680867532127,offset:8,opacity:1},{angle:1.8849555921538759,size:.3209021989338088,offset:8,opacity:1},{angle:1.902408884673819,size:0,offset:8,opacity:1},{angle:1.9198621771937625,size:0,offset:8,opacity:1},{angle:1.9373154697137058,size:.26002544806143374,offset:8,opacity:1},{angle:1.9547687622336491,size:.2924936993062236,offset:8,opacity:1},{angle:1.9722220547535922,size:.43140240061138796,offset:8,opacity:1},{angle:1.9896753472735356,size:.29591579647411836,offset:8,opacity:1},{angle:2.007128639793479,size:.46532747343985814,offset:8,opacity:1},{angle:2.0245819323134224,size:0,offset:8,opacity:1},{angle:2.0420352248333655,size:.37989836106928254,offset:8,opacity:1},{angle:2.0594885173533086,size:.43824671847111324,offset:8,opacity:1},{angle:2.076941809873252,size:.21491306461629336,offset:8,opacity:1},{angle:2.0943951023931953,size:.2576066045616476,offset:8,opacity:1},{angle:2.111848394913139,size:.20559969896825836,offset:8,opacity:1},{angle:2.129301687433082,size:.5452053035796387,offset:8,opacity:1},{angle:2.1467549799530254,size:.4317948579735969,offset:8,opacity:1},{angle:2.1642082724729685,size:.2926508010599716,offset:8,opacity:1},{angle:2.1816615649929116,size:.37646244630618103,offset:8,opacity:1},{angle:2.199114857512855,size:.5182160912889464,offset:8,opacity:1},{angle:2.2165681500327987,size:.5838728943805604,offset:8,opacity:1},{angle:2.234021442552742,size:.21844249399465382,offset:8,opacity:1},{angle:2.251474735072685,size:0,offset:8,opacity:1},{angle:2.2689280275926285,size:.3721009993624145,offset:8,opacity:1},{angle:2.2863813201125716,size:0,offset:8,opacity:1},{angle:2.3038346126325147,size:.24598938578156437,offset:8,opacity:1},{angle:2.321287905152458,size:.4507505938631045,offset:8,opacity:1},{angle:2.3387411976724017,size:.25469635650569583,offset:8,opacity:1},{angle:2.356194490192345,size:.30528741051655217,offset:8,opacity:1},{angle:2.373647782712288,size:.37088412070072785,offset:8,opacity:1},{angle:2.3911010752322315,size:.24486197147462863,offset:8,opacity:1},{angle:2.408554367752175,size:0,offset:8,opacity:1},{angle:2.426007660272118,size:.43989471208136854,offset:8,opacity:1},{angle:2.443460952792061,size:.33696542573155486,offset:8,opacity:1},{angle:2.4609142453120048,size:0,offset:8,opacity:1},{angle:2.478367537831948,size:0,offset:8,opacity:1},{angle:2.495820830351891,size:0,offset:8,opacity:1},{angle:2.5132741228718345,size:.2505063689411901,offset:8,opacity:1},{angle:2.530727415391778,size:.31438011396387455,offset:8,opacity:1},{angle:2.548180707911721,size:.34374426546984016,offset:8,opacity:1},{angle:2.5656340004316642,size:.2305610481543743,offset:8,opacity:1},{angle:2.5830872929516078,size:.37268657957858453,offset:8,opacity:1},{angle:2.600540585471551,size:.25219421624230426,offset:8,opacity:1},{angle:2.6179938779914944,size:0,offset:8,opacity:1},{angle:2.6354471705114375,size:.23021680363052838,offset:8,opacity:1},{angle:2.652900463031381,size:.3483359449322281,offset:8,opacity:1},{angle:2.670353755551324,size:.3251780474107786,offset:8,opacity:1},{angle:2.6878070480712672,size:0,offset:8,opacity:1},{angle:2.705260340591211,size:.3687577362310519,offset:8,opacity:1},{angle:2.7227136331111543,size:.5694461018098402,offset:8,opacity:1},{angle:2.7401669256310974,size:.22425733709526832,offset:8,opacity:1},{angle:2.7576202181510405,size:0,offset:8,opacity:1},{angle:2.7750735106709836,size:.32709123488963454,offset:8,opacity:1},{angle:2.792526803190927,size:.3643797544475289,offset:8,opacity:1},{angle:2.8099800957108707,size:.25481296814968835,offset:8,opacity:1},{angle:2.827433388230814,size:.20233815801319835,offset:8,opacity:1},{angle:2.844886680750757,size:.22289439004543232,offset:8,opacity:1},{angle:2.8623399732707004,size:0,offset:8,opacity:1},{angle:2.8797932657906435,size:0,offset:8,opacity:1},{angle:2.897246558310587,size:0,offset:8,opacity:1},{angle:2.91469985083053,size:.22662095056906018,offset:8,opacity:1},{angle:2.9321531433504737,size:.6177358599983805,offset:8,opacity:1},{angle:2.949606435870417,size:.5579600289881892,offset:8,opacity:1},{angle:2.96705972839036,size:.24919239443796898,offset:8,opacity:1},{angle:2.9845130209103035,size:0,offset:8,opacity:1},{angle:3.001966313430247,size:.20285345071151972,offset:8,opacity:1},{angle:3.01941960595019,size:.29633213437720063,offset:8,opacity:1},{angle:3.036872898470133,size:.31615740448077223,offset:8,opacity:1},{angle:3.0543261909900767,size:.4883995719883713,offset:8,opacity:1},{angle:3.07177948351002,size:.2500925025911332,offset:8,opacity:1},{angle:3.089232776029963,size:.262931178068741,offset:8,opacity:1},{angle:3.1066860685499065,size:.3135512137978654,offset:8,opacity:1},{angle:3.12413936106985,size:.31083588965839803,offset:8,opacity:1},{angle:3.141592653589793,size:0,offset:8,opacity:1},{angle:3.159045946109736,size:.24804439339468767,offset:8,opacity:1},{angle:3.1764992386296798,size:0,offset:8,opacity:1},{angle:3.193952531149623,size:.2571395452468249,offset:8,opacity:1},{angle:3.211405823669566,size:.6279202198461746,offset:8,opacity:1},{angle:3.2288591161895095,size:.24288586668685336,offset:8,opacity:1},{angle:3.246312408709453,size:.34718500726687895,offset:8,opacity:1},{angle:3.2637657012293966,size:.47061815108690846,offset:8,opacity:1},{angle:3.2812189937493397,size:0,offset:8,opacity:1},{angle:3.2986722862692828,size:.4866126208626991,offset:8,opacity:1},{angle:3.3161255787892263,size:0,offset:8,opacity:1},{angle:3.3335788713091694,size:0,offset:8,opacity:1},{angle:3.3510321638291125,size:.22976093136313686,offset:8,opacity:1},{angle:3.368485456349056,size:0,offset:8,opacity:1},{angle:3.385938748868999,size:0,offset:8,opacity:1},{angle:3.4033920413889422,size:.4397259724357342,offset:8,opacity:1},{angle:3.4208453339088853,size:.24667841492062575,offset:8,opacity:1},{angle:3.4382986264288293,size:.43391379529064145,offset:8,opacity:1},{angle:3.455751918948773,size:0,offset:8,opacity:1},{angle:3.473205211468716,size:0,offset:8,opacity:1},{angle:3.490658503988659,size:.21961175000891414,offset:8,opacity:1},{angle:3.5081117965086026,size:.36890033272657746,offset:8,opacity:1},{angle:3.5255650890285457,size:.4326945036689108,offset:8,opacity:1},{angle:3.543018381548489,size:.3268470087082487,offset:8,opacity:1},{angle:3.560471674068432,size:.20854219238334568,offset:8,opacity:1},{angle:3.5779249665883754,size:.2423254936922336,offset:8,opacity:1},{angle:3.5953782591083185,size:.4064744910955269,offset:8,opacity:1},{angle:3.6128315516282616,size:0,offset:8,opacity:1},{angle:3.630284844148205,size:.31287388031447483,offset:8,opacity:1},{angle:3.647738136668149,size:.3948160804791036,offset:8,opacity:1},{angle:3.6651914291880923,size:0,offset:8,opacity:1},{angle:3.6826447217080354,size:.2026956152601606,offset:8,opacity:1},{angle:3.7000980142279785,size:0,offset:8,opacity:1},{angle:3.717551306747922,size:.44054519647144813,offset:8,opacity:1},{angle:3.735004599267865,size:.4130009051490618,offset:8,opacity:1},{angle:3.752457891787808,size:0,offset:8,opacity:1},{angle:3.7699111843077517,size:.49311184417141246,offset:8,opacity:1},{angle:3.787364476827695,size:.45508968556466084,offset:8,opacity:1},{angle:3.804817769347638,size:0,offset:8,opacity:1},{angle:3.8222710618675815,size:.48005037780936805,offset:8,opacity:1},{angle:3.839724354387525,size:0,offset:8,opacity:1},{angle:3.8571776469074686,size:.4561164475672816,offset:8,opacity:1},{angle:3.8746309394274117,size:.3002000652009621,offset:8,opacity:1},{angle:3.8920842319473548,size:.3336541884527151,offset:8,opacity:1},{angle:3.9095375244672983,size:0,offset:8,opacity:1},{angle:3.9269908169872414,size:.4811384274609927,offset:8,opacity:1},{angle:3.9444441095071845,size:0,offset:8,opacity:1},{angle:3.961897402027128,size:.5102504861379599,offset:8,opacity:1},{angle:3.979350694547071,size:.23214101697543765,offset:8,opacity:1},{angle:3.9968039870670142,size:.29156434123379016,offset:8,opacity:1},{angle:4.014257279586958,size:0,offset:8,opacity:1},{angle:4.031710572106902,size:.20004717274584805,offset:8,opacity:1},{angle:4.049163864626845,size:.3614701974794731,offset:8,opacity:1},{angle:4.066617157146788,size:0,offset:8,opacity:1},{angle:4.084070449666731,size:.2219991701875027,offset:8,opacity:1},{angle:4.101523742186674,size:.22431067260162876,offset:8,opacity:1},{angle:4.118977034706617,size:.30926005945781443,offset:8,opacity:1},{angle:4.136430327226561,size:.2513871895964691,offset:8,opacity:1},{angle:4.153883619746504,size:.2659224601694766,offset:8,opacity:1},{angle:4.171336912266447,size:.2216658406470619,offset:8,opacity:1},{angle:4.1887902047863905,size:.4273566387084955,offset:8,opacity:1},{angle:4.206243497306334,size:.34036553217806864,offset:8,opacity:1},{angle:4.223696789826278,size:.32533678816516465,offset:8,opacity:1},{angle:4.241150082346221,size:.23594875228493112,offset:8,opacity:1},{angle:4.258603374866164,size:0,offset:8,opacity:1},{angle:4.276056667386108,size:0,offset:8,opacity:1},{angle:4.293509959906051,size:.31051254932537203,offset:8,opacity:1},{angle:4.310963252425994,size:0,offset:8,opacity:1},{angle:4.328416544945937,size:0,offset:8,opacity:1},{angle:4.34586983746588,size:0,offset:8,opacity:1},{angle:4.363323129985823,size:.41758554970783024,offset:8,opacity:1},{angle:4.380776422505767,size:0,offset:8,opacity:1},{angle:4.39822971502571,size:.5165181485571547,offset:8,opacity:1},{angle:4.4156830075456535,size:0,offset:8,opacity:1},{angle:4.4331363000655974,size:0,offset:8,opacity:1},{angle:4.4505895925855405,size:.2875659335408256,offset:8,opacity:1},{angle:4.468042885105484,size:.2630697344221547,offset:8,opacity:1},{angle:4.485496177625427,size:.3044067435482997,offset:8,opacity:1},{angle:4.50294947014537,size:.3224161157725142,offset:8,opacity:1},{angle:4.520402762665314,size:.29296045447988417,offset:8,opacity:1},{angle:4.537856055185257,size:0,offset:8,opacity:1},{angle:4.5553093477052,size:.5348621754537155,offset:8,opacity:1},{angle:4.572762640225143,size:.23724437888510921,offset:8,opacity:1},{angle:4.590215932745086,size:0,offset:8,opacity:1},{angle:4.607669225265029,size:.23471290119278718,offset:8,opacity:1},{angle:4.625122517784973,size:.23001169467314136,offset:8,opacity:1},{angle:4.642575810304916,size:.3381947981140539,offset:8,opacity:1},{angle:4.66002910282486,size:0,offset:8,opacity:1},{angle:4.6774823953448035,size:.2820194526396866,offset:8,opacity:1},{angle:4.694935687864747,size:.27589340223228825,offset:8,opacity:1},{angle:4.71238898038469,size:0,offset:8,opacity:1},{angle:4.729842272904633,size:0,offset:8,opacity:1},{angle:4.747295565424576,size:0,offset:8,opacity:1},{angle:4.764748857944519,size:0,offset:8,opacity:1},{angle:4.782202150464463,size:.6047061570324029,offset:8,opacity:1},{angle:4.799655442984406,size:0,offset:8,opacity:1},{angle:4.81710873550435,size:0,offset:8,opacity:1},{angle:4.834562028024293,size:.4385393337320528,offset:8,opacity:1},{angle:4.852015320544236,size:0,offset:8,opacity:1},{angle:4.869468613064179,size:.3738861085343654,offset:8,opacity:1},{angle:4.886921905584122,size:.2369083255375507,offset:8,opacity:1},{angle:4.9043751981040655,size:.20042730995532476,offset:8,opacity:1},{angle:4.9218284906240095,size:.27407857191507573,offset:8,opacity:1},{angle:4.939281783143953,size:.260307602595212,offset:8,opacity:1},{angle:4.956735075663896,size:.22693010922108042,offset:8,opacity:1},{angle:4.974188368183839,size:0,offset:8,opacity:1},{angle:4.991641660703782,size:.36404125447608293,offset:8,opacity:1},{angle:5.009094953223726,size:.36660145102645764,offset:8,opacity:1},{angle:5.026548245743669,size:.21844437325488894,offset:8,opacity:1},{angle:5.044001538263612,size:0,offset:8,opacity:1},{angle:5.061454830783556,size:0,offset:8,opacity:1},{angle:5.078908123303499,size:0,offset:8,opacity:1},{angle:5.096361415823442,size:.6203742746502853,offset:8,opacity:1},{angle:5.113814708343385,size:0,offset:8,opacity:1},{angle:5.1312680008633285,size:0,offset:8,opacity:1},{angle:5.148721293383272,size:0,offset:8,opacity:1},{angle:5.1661745859032155,size:0,offset:8,opacity:1},{angle:5.183627878423159,size:.4394142349769681,offset:8,opacity:1},{angle:5.201081170943102,size:.46186021808530575,offset:8,opacity:1},{angle:5.218534463463046,size:.29002729040435316,offset:8,opacity:1},{angle:5.235987755982989,size:0,offset:8,opacity:1},{angle:5.253441048502932,size:0,offset:8,opacity:1},{angle:5.270894341022875,size:0,offset:8,opacity:1},{angle:5.288347633542818,size:.2776981969296649,offset:8,opacity:1},{angle:5.305800926062762,size:0,offset:8,opacity:1},{angle:5.323254218582705,size:.3066397012962211,offset:8,opacity:1},{angle:5.340707511102648,size:.22441601610004672,offset:8,opacity:1},{angle:5.358160803622591,size:.25396857567416625,offset:8,opacity:1},{angle:5.3756140961425345,size:.5404051622973916,offset:8,opacity:1},{angle:5.393067388662478,size:.274204802103521,offset:8,opacity:1},{angle:5.410520681182422,size:.2565405657819229,offset:8,opacity:1},{angle:5.427973973702365,size:.3725761880437103,offset:8,opacity:1},{angle:5.445427266222309,size:0,offset:8,opacity:1},{angle:5.462880558742252,size:0,offset:8,opacity:1},{angle:5.480333851262195,size:.25731937569411467,offset:8,opacity:1},{angle:5.497787143782138,size:.3188635809253325,offset:8,opacity:1},{angle:5.515240436302081,size:.3356669541977725,offset:8,opacity:1},{angle:5.532693728822024,size:.20400657089323843,offset:8,opacity:1},{angle:5.550147021341967,size:0,offset:8,opacity:1},{angle:5.567600313861911,size:0,offset:8,opacity:1},{angle:5.585053606381854,size:0,offset:8,opacity:1},{angle:5.602506898901798,size:.3165635361425635,offset:8,opacity:1},{angle:5.619960191421741,size:.3235314722259551,offset:8,opacity:1},{angle:5.6374134839416845,size:.26584898777242194,offset:8,opacity:1},{angle:5.654866776461628,size:.21243062483790226,offset:8,opacity:1},{angle:5.672320068981571,size:.30724136900399,offset:8,opacity:1},{angle:5.689773361501514,size:.2501099291145934,offset:8,opacity:1},{angle:5.707226654021458,size:.39400265901275955,offset:8,opacity:1},{angle:5.724679946541401,size:.2093418399866516,offset:8,opacity:1},{angle:5.742133239061344,size:.32923152005471246,offset:8,opacity:1},{angle:5.759586531581287,size:0,offset:8,opacity:1},{angle:5.77703982410123,size:.56456531278707,offset:8,opacity:1},{angle:5.794493116621174,size:.20557753914211502,offset:8,opacity:1},{angle:5.811946409141117,size:.5684634070194285,offset:8,opacity:1},{angle:5.82939970166106,size:.20249056891565564,offset:8,opacity:1},{angle:5.846852994181004,size:.3190036119950446,offset:8,opacity:1},{angle:5.8643062867009474,size:0,offset:8,opacity:1},{angle:5.8817595792208905,size:.20533088638096786,offset:8,opacity:1},{angle:5.899212871740834,size:.3241677112796085,offset:8,opacity:1},{angle:5.916666164260777,size:.3624193559997502,offset:8,opacity:1},{angle:5.93411945678072,size:.40343678768322566,offset:8,opacity:1},{angle:5.951572749300664,size:.4188672613641715,offset:8,opacity:1},{angle:5.969026041820607,size:.2285210234832376,offset:8,opacity:1},{angle:5.98647933434055,size:0,offset:8,opacity:1},{angle:6.003932626860494,size:.48733967322430105,offset:8,opacity:1},{angle:6.021385919380437,size:.24186501745395364,offset:8,opacity:1},{angle:6.03883921190038,size:.4584926710071769,offset:8,opacity:1},{angle:6.056292504420323,size:.25479465338552776,offset:8,opacity:1},{angle:6.073745796940266,size:.2281956836229175,offset:8,opacity:1},{angle:6.09119908946021,size:.29373851820189983,offset:8,opacity:1},{angle:6.1086523819801535,size:.3027785858358294,offset:8,opacity:1},{angle:6.126105674500097,size:.39200941647226684,offset:8,opacity:1},{angle:6.14355896702004,size:.5167887646509625,offset:8,opacity:1},{angle:6.161012259539983,size:.48184476186013575,offset:8,opacity:1},{angle:6.178465552059926,size:.2170741818198825,offset:8,opacity:1},{angle:6.19591884457987,size:0,offset:8,opacity:1},{angle:6.213372137099813,size:.20735775148015026,offset:8,opacity:1},{angle:6.230825429619757,size:.38122080663892877,offset:8,opacity:1},{angle:6.2482787221397,size:.25023583869705845,offset:8,opacity:1},{angle:6.265732014659643,size:.2303276717210175,offset:8,opacity:1}],roughEdgeProbability:.3,roughEdgeShift:8,roughEdgePoints:360},F1={showRuler:!0,showFullRuler:!0,showCrossLine:!0,showDashLine:!0,showSideRuler:!0,showCurrentPositionText:!0},k1={svgPath:"M 0 -1 L 0.588 0.809 L -0.951 -0.309 L 0.951 -0.309 L -0.588 0.809 Z",drawStar:!1,starDiameter:14,starPositionY:0,scaleToSmallStar:!1},U1={openSecurityPattern:!0,securityPatternWidth:.15,securityPatternLength:3,securityPatternCount:5,securityPatternAngleRange:40,securityPatternParams:[{angle:1.409748801283011,lineAngle:.1920074644466812},{angle:5.32114078029858,lineAngle:2.6432851725092466},{angle:4.1098611216984136,lineAngle:-2.5979078269198292},{angle:4.994408472565594,lineAngle:2.8406667666143948},{angle:2.962601457171679,lineAngle:-1.5064351132749016}]},W1={companyName:"印章绘制有限责任公司",compression:1,borderOffset:1,textDistributionFactor:5,fontFamily:"Songti SC",fontHeight:4.2,fontWeight:400,shape:"ellipse",adjustEllipseText:!1,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"counterclockwise"},$1={code:"1234567890",compression:1,fontHeight:1.2,fontFamily:"Arial",borderOffset:1,fontWidth:1.2,textDistributionFactor:50,fontWeight:"normal"},V1=40,H1=40,j1={stampType:"印章类型",fontHeight:4.6,fontFamily:"Arial",fontWidth:3,compression:.75,letterSpacing:0,positionY:-3,fontWeight:"normal",lineSpacing:2},Y1="blue",B1=1,X1=!1,G1=!1,K1={code:"",compression:.7,fontHeight:3.7,fontFamily:"Arial",fontWidth:1.3,letterSpacing:8,positionY:0,totalWidth:26,fontWeight:"normal"},J1={applyAging:!1,agingIntensity:50,agingEffectParams:[]},Q1={drawInnerCircle:!0,innerCircleLineWidth:.5,innerCircleLineRadiusX:16,innerCircleLineRadiusY:12},q1={drawInnerCircle:!0,innerCircleLineWidth:.2,innerCircleLineRadiusX:36,innerCircleLineRadiusY:27},Z1=!1,ep=[{stampType:"印章类型",fontHeight:4.6,fontFamily:"Arial",fontWidth:3,compression:.75,letterSpacing:0,positionY:.5,fontWeight:"normal",lineSpacing:2}],tp=[{companyName:"模板2印章有限责任公司",compression:1,borderOffset:1,textDistributionFactor:3,fontFamily:"SimSun",fontHeight:4.2,fontWeight:"normal",shape:"ellipse",adjustEllipseText:!0,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"counterclockwise"}],sp=[],np=[{imageUrl:"data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",imageWidth:16.5,imageHeight:16,positionX:0,positionY:-2,keepAspectRatio:!0}],ip=1,op=0,ap=0,lp=0,rp={drawInnerCircle:!0,innerCircleLineWidth:1,innerCircleLineRadiusX:20,innerCircleLineRadiusY:15},cp={roughEdge:R1,ruler:F1,drawStar:k1,securityPattern:U1,company:W1,stampCode:$1,width:V1,height:H1,stampType:j1,primaryColor:Y1,borderWidth:B1,refreshSecurityPattern:X1,refreshOld:G1,taxNumber:K1,agingEffect:J1,innerCircle:Q1,outThinCircle:q1,openManualAging:Z1,stampTypeList:ep,companyList:tp,innerCircleList:sp,imageList:np,scale:ip,offsetX:op,offsetY:ap,mmToPixel:lp,outBorder:rp},fp={class:"control-group"},up={class:"group-content"},pp={class:"checkbox-label"},dp={class:"checkbox-label"},gp={key:0},mp={class:"control-group"},hp={class:"group-content"},yp={class:"company-header"},vp=["onClick"],_p=["onUpdate:modelValue"],bp={class:"font-input-group"},Cp=["onUpdate:modelValue"],Sp=["value"],Tp=["onUpdate:modelValue","placeholder"],wp=["onUpdate:modelValue"],Ep=["onUpdate:modelValue"],zp={value:"normal"},Pp={value:"bold"},Ip=["onUpdate:modelValue"],Lp=["onUpdate:modelValue"],xp=["onUpdate:modelValue"],Mp={class:"range-container"},Ap=["onUpdate:modelValue"],Np=["onUpdate:modelValue"],Dp={value:"clockwise"},Op={value:"counterclockwise"},Rp={class:"control-group"},Fp={class:"group-content"},kp={class:"stamp-type-header"},Up=["onClick"],Wp=["onUpdate:modelValue"],$p=["onUpdate:modelValue"],Vp={class:"font-input-group"},Hp=["onUpdate:modelValue"],jp={id:"stampTypeFontList"},Yp=["value"],Bp=["onUpdate:modelValue"],Xp=["onUpdate:modelValue"],Gp=["onUpdate:modelValue"],Kp=["onUpdate:modelValue"],Jp={class:"control-group stamp-code-group"},Qp={class:"group-content"},qp={class:"font-input-group"},Zp=["value"],ed={value:"normal"},td={value:"bold"},sd={class:"control-group tax-number-group"},nd={class:"group-content"},id={class:"font-input-group"},od=["value"],ad={class:"control-group"},ld={class:"group-content"},rd={class:"image-header"},cd=["onClick"],fd={key:0,class:"image-preview"},ud=["src","alt"],pd=["onChange"],dd=["onUpdate:modelValue"],gd=["onUpdate:modelValue"],md=["onUpdate:modelValue"],hd=["onUpdate:modelValue"],yd={class:"checkbox-label"},vd=["onUpdate:modelValue"],_d={class:"control-group"},bd={class:"group-content"},Cd={class:"checkbox-label"},Sd={key:0},Td={class:"control-group"},wd={class:"group-content"},Ed={class:"control-group"},zd={class:"group-content"},Pd={class:"checkbox-label"},Id={key:0},Ld={key:1},xd={key:2},Md={key:3},Ad={key:4},Nd={class:"control-group"},Dd={class:"group-content"},Od={class:"checkbox-label"},Rd={class:"checkbox-label"},Fd={key:0},kd={class:"control-group"},Ud={class:"group-content"},Wd={class:"inner-circle-header"},$d=["onClick"],Vd=["onUpdate:modelValue"],Hd=["onUpdate:modelValue"],jd=["onUpdate:modelValue"],Yd=hs({__name:"EditorControls",props:{drawStampUtils:{}},emits:["updateDrawStamp"],setup(e,{expose:t,emit:s}){const{t:n}=ys(),i=V(null),o=e,a=s,l=V(!0),r=V("绘制印章有限责任公司"),u=V("1234567890123"),f=V("000000000000000000"),d=V("Songti SC"),h=V(4.2),T=V("SimSun"),L=V(1.2),M=V(1.2),U=V(20),y=V(1),w=V("blue"),x=V(14),b=V(!1),D=V(!1),$=V(50),R=V(3),se=V(!1),fe=V(.5),ve=V(1),_e=V(1),ce=V(20),je=V("合同专用章"),Ze=V("SimSun"),dt=V(4.6),xe=V(3),ge=V(0),re=V(0),Me=V(-5),et=V(1),Ye=V(400),q=V(400),G=V(400),ee=V("Songti SC"),Ie=V(400),pe=V(1),ne=V(1),Oe=V(!0),Bt=V(.5),tt=V(.2),Mt=V("#FF0000"),gt=V(5),m=V(2);V(!1);const v=V(!1),P=V(1),W=V(.3),k=V(0),c=V(!0),g=V(.5),S=V(15),I=V(12),N=V(!0),H=V(.5),_=V(25),z=V(22);V(null);const j=V(!1),Q=V(.2),oe=V(5),ae=V(.5),Ee=V(8),Ae=V(360);V(!1);const Re=V([{stampType:"印章类型",fontHeight:4.6,fontFamily:"SimSun",compression:.75,letterSpacing:0,positionY:-3,fontWeight:"normal",lineSpacing:2,fontWidth:3}]),De=V([{companyName:"绘制印章有限责任公司",compression:1,borderOffset:1,textDistributionFactor:3,fontFamily:"SimSun",fontHeight:4.2,fontWeight:"normal",shape:"ellipse",adjustEllipseText:!1,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"counterclockwise"}]),At=V(!1),vs=V(10),Be=V(10),ot=V(!0),Nt=V([{drawInnerCircle:!0,innerCircleLineWidth:.5,innerCircleLineRadiusX:36,innerCircleLineRadiusY:27},{drawInnerCircle:!0,innerCircleLineWidth:.5,innerCircleLineRadiusX:16,innerCircleLineRadiusY:12}]);V(null);const mt=V([{imageUrl:"",imageWidth:10,imageHeight:10,positionX:0,positionY:0,keepAspectRatio:!0}]),_s=V(!0),Us=V(1),lr=()=>{console.log("add new image",mt.value),(mt.value===void 0||mt.value===null)&&(mt.value=[]),mt.value.length<10&&mt.value.push({imageUrl:"",imageWidth:10,imageHeight:10,positionX:0,positionY:0,keepAspectRatio:!0})},rr=O=>{mt.value.splice(O,1)},cr=(O,E)=>{const C=O.target;if(C.files&&C.files[0]){const le=C.files[0],Y=new FileReader;Y.onload=ke=>{var bt;(bt=ke.target)!=null&&bt.result&&(mt.value[E].imageUrl=ke.target.result,Vs())},Y.readAsDataURL(le)}},fr=()=>{let O=-3;if(Re.value.length>0){const E=Re.value[Re.value.length-1];O=E.positionY+E.fontHeight}Re.value.push({stampType:"新印章类型",fontHeight:4,fontFamily:"SimSun",compression:.75,letterSpacing:0,positionY:O,fontWeight:"normal",lineSpacing:2,fontWidth:3})},ur=O=>{Re.value.splice(O,1)},pr=()=>{let O=1;if(De.value.length>0){const E=De.value[De.value.length-1];O=E.borderOffset+E.fontHeight}De.value.push({companyName:"新公司名称",compression:1,borderOffset:O,textDistributionFactor:3,fontFamily:"SimSun",fontHeight:4.2,fontWeight:"normal",shape:"ellipse",adjustEllipseText:!1,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"counterclockwise"})},dr=O=>{De.value.splice(O,1)},Ws=V(40),$s=V(30),ii=V(1.2),Vs=(O=!1,E=!1,C=!1)=>{o.drawStampUtils.refreshStamp(O,E,C),a("updateDrawStamp",o.drawStampUtils.getDrawConfigs(),O,E,C)},gr=()=>{Nt.value.push({drawInnerCircle:!0,innerCircleLineWidth:.5,innerCircleLineRadiusX:16,innerCircleLineRadiusY:12})},mr=O=>{Nt.value.splice(O,1)},hr=()=>{const O=o.drawStampUtils.getDrawConfigs(),E=O.agingEffect;E.applyAging=b.value,E.agingIntensity=$.value,O.openManualAging=D.value;const C=O.securityPattern;C.openSecurityPattern=Oe.value,C.securityPatternCount=gt.value,C.securityPatternWidth=tt.value,C.securityPatternLength=m.value;const le=O.company;le.companyName=r.value,le.textDistributionFactor=R.value,le.borderOffset=ve.value,le.fontHeight=h.value,le.fontFamily=d.value,le.compression=et.value,le.fontWeight=Ye.value,le.adjustEllipseText=se.value,le.adjustEllipseTextFactor=fe.value;const Y=O.taxNumber;Y.code=f.value,Y.compression=P.value,Y.positionY=k.value,Y.letterSpacing=W.value,Y.fontFamily=ee.value,Y.fontWeight=Ie.value;const ke=O.stampType;ke.stampType=je.value,ke.fontFamily=Ze.value,ke.fontHeight=dt.value,ke.fontWidth=xe.value,ke.letterSpacing=ge.value,ke.positionY=Me.value,ke.compression=pe.value,ke.fontWeight=q.value,ke.lineSpacing=ii.value;const bt=O.stampCode;bt.code=u.value,bt.compression=ne.value,bt.fontFamily=T.value,bt.fontHeight=L.value,bt.fontWidth=M.value,bt.borderOffset=_e.value,bt.textDistributionFactor=ce.value,bt.fontWeight=G.value,O.primaryColor=w.value,O.borderWidth=y.value,O.width=Ws.value,O.height=$s.value;const as=O.drawStar;as.drawStar=v.value,as.useImage=At.value,as.imageWidth=vs.value,as.imageHeight=Be.value,as.keepAspectRatio=ot.value,as.starDiameter=x.value,as.starPositionY=re.value;const bs=O.roughEdge;bs.drawRoughEdge=j.value,bs.roughEdgeWidth=Q.value,bs.roughEdgeHeight=oe.value,bs.roughEdgeProbability=ae.value,bs.roughEdgeShift=Ee.value,bs.roughEdgePoints=Ae.value;const vn=O.innerCircle;vn.drawInnerCircle=c.value,vn.innerCircleLineWidth=g.value,vn.innerCircleLineRadiusX=S.value,vn.innerCircleLineRadiusY=I.value;const _n=O.outThinCircle;_n.drawInnerCircle=N.value,_n.innerCircleLineWidth=H.value,_n.innerCircleLineRadiusX=_.value,_n.innerCircleLineRadiusY=z.value,O.stampTypeList=Re.value,O.companyList=De.value,O.innerCircleList=Nt.value,O.imageList=mt.value;const Co=O.outBorder;Co.drawInnerCircle=_s.value,Co.innerCircleLineWidth=Us.value,Vs()},_o=()=>{const O=o.drawStampUtils.getDrawConfigs();b.value=O.agingEffect.applyAging,$.value=O.agingEffect.agingIntensity,D.value=O.openManualAging,Oe.value=O.securityPattern.openSecurityPattern,gt.value=O.securityPattern.securityPatternCount,tt.value=O.securityPattern.securityPatternWidth,m.value=O.securityPattern.securityPatternLength,j.value=O.roughEdge.drawRoughEdge,Q.value=O.roughEdge.roughEdgeWidth,oe.value=O.roughEdge.roughEdgeHeight,ae.value=O.roughEdge.roughEdgeProbability,Ee.value=O.roughEdge.roughEdgeShift,Ae.value=O.roughEdge.roughEdgePoints,Ws.value=O.width,$s.value=O.height,y.value=O.borderWidth,w.value=O.primaryColor,r.value=O.company.companyName,h.value=O.company.fontHeight,et.value=O.company.compression,R.value=O.company.textDistributionFactor,ve.value=O.company.borderOffset,De.value=O.companyList;const E=O.stampCode;u.value=E.code,L.value=E.fontHeight,M.value=E.fontWidth,ce.value=E.textDistributionFactor,_e.value=E.borderOffset,T.value=E.fontFamily,G.value=E.fontWeight,ne.value=E.compression;const C=O.taxNumber;f.value=C.code,P.value=C.compression,W.value=C.letterSpacing,k.value=C.positionY,ee.value=C.fontFamily,Ie.value=C.fontWeight;const le=O.stampType;je.value=le.stampType,dt.value=le.fontHeight,xe.value=le.fontWidth,ge.value=le.letterSpacing,Me.value=le.positionY,Ze.value=le.fontFamily,q.value=le.fontWeight,pe.value=le.compression,ii.value=le.lineSpacing,Re.value=O.stampTypeList,v.value=O.drawStar.drawStar,At.value=O.drawStar.useImage,vs.value=O.drawStar.imageWidth,Be.value=O.drawStar.imageHeight,ot.value=O.drawStar.keepAspectRatio,x.value=O.drawStar.starDiameter,re.value=O.drawStar.starPositionY,c.value=O.innerCircle.drawInnerCircle,g.value=O.innerCircle.innerCircleLineWidth,S.value=O.innerCircle.innerCircleLineRadiusX,I.value=O.innerCircle.innerCircleLineRadiusY,Nt.value=O.innerCircleList,N.value=O.outThinCircle.drawInnerCircle,H.value=O.outThinCircle.innerCircleLineWidth,_.value=O.outThinCircle.innerCircleLineRadiusX,z.value=O.outThinCircle.innerCircleLineRadiusY,mt.value=O.imageList||[],_s.value=O.outBorder.drawInnerCircle,Us.value=O.outBorder.innerCircleLineWidth},Hs=V([]),yr=async()=>{Hs.value=await or()};Kn(async()=>{console.log("onMounted drawStampUtils",o.drawStampUtils),await yr(),_o(),Vs(),_r(),document.querySelectorAll(".font-select, .font-input").forEach(O=>{O instanceof HTMLElement&&os({target:O})})}),vt([r,d,u,h,L,U,y,w,x,ce,R,ve,_e,$,je,Ze,dt,ge,Me,f,b,$,et,pe,ne,ge,Mt,Bt,Mt,Oe,gt,m,tt,Ws,$s,v,re,P,ee,W,k,x,c,g,S,I,H,_,z,N,D,j,Q,oe,ae,Ee,Ae,Ye,q,G,T,Ie,se,fe,ii,Re,De,At,vs,Be,ot,Nt,mt,_s,Us],()=>{hr()},{deep:!0});const oi=V([{id:"contract",name:"合同专用章",text:"合同专用章",fontSize:4.6,letterSpacing:0,lineSpacing:1.2,positionY:-5,compression:1},{id:"invoice",name:"印章类型",text:`发票专章
增值税专用`,fontSize:4.2,letterSpacing:0,lineSpacing:1.5,positionY:-4,compression:.9},{id:"finance",name:"财务专用章",text:`财务专用章
仅限报销使用`,fontSize:4,letterSpacing:0,lineSpacing:1.8,positionY:-3,compression:.85}]),vr=()=>{localStorage.setItem("stampTypePresets",JSON.stringify(oi.value))},_r=()=>{const O=localStorage.getItem("stampTypePresets");O&&(oi.value=JSON.parse(O))};vt(oi,()=>{vr()},{deep:!0});const os=O=>{var le,Y;const E=O.target,C=(E.tagName==="SELECT",E.value);if(E.style.setProperty("--current-font",C),E.tagName==="SELECT"){const ke=(le=E.parentElement)==null?void 0:le.querySelector(".font-input");ke&&(ke.value=C,ke.style.setProperty("--current-font",C))}if(E.tagName==="INPUT"){const ke=(Y=E.parentElement)==null?void 0:Y.querySelector(".font-select");ke&&(ke.value=C,ke.style.setProperty("--current-font",C))}},Te=V({basic:!1,company:!1,stampType:!1,code:!1,taxNumber:!1,star:!1,security:!1,roughEdge:!1,aging:!1,innerCircle:!1,images:!1}),_t=O=>{Te.value[O]=!Te.value[O]},bo=V(localStorage.getItem("showSecurityWarning")!=="false");vt(bo,O=>{localStorage.setItem("showSecurityWarning",String(O))});const ai=()=>{const O=o.drawStampUtils.getDrawConfigs();O.agingEffect={...O.agingEffect,applyAging:b.value,manualAging:D.value,intensity:$.value},a("updateDrawStamp",O,!1,!0,!1)},br=()=>{a("updateDrawStamp",o.drawStampUtils.getDrawConfigs(),!1,!0,!1)};return t({scrollToCompanyText:O=>{Te.value.company=!0,zs(()=>{const E=document.querySelector(`.company-item:nth-child(${O+1})`);E&&E.scrollIntoView({behavior:"smooth",block:"center"})})},scrollToCode:()=>{Te.value.code=!0,zs(()=>{const O=document.querySelector(".stamp-code-group");O&&O.scrollIntoView({behavior:"smooth",block:"start"})})},scrollToStampType:O=>{Te.value.stampType=!0,zs(()=>{const E=document.querySelector(`.stamp-type-item:nth-child(${O+1})`);E&&E.scrollIntoView({behavior:"smooth",block:"center"})})},scrollToTaxNumber:()=>{Te.value.taxNumber=!0,zs(()=>{const O=document.querySelector(".tax-number-group");O&&O.scrollIntoView({behavior:"smooth",block:"start"})})},restoreDrawConfigs:_o}),(O,E)=>(de(),me("div",{class:Ue(["container",{"has-warning":bo.value}])},[p("div",{class:"editor-controls",ref_key:"editorControls",ref:i},[p("div",fp,[p("div",{class:"group-header",onClick:E[0]||(E[0]=C=>_t("basic"))},[p("h3",null,[X(A(F(n)("stamp.basic.title")),1),p("span",{class:Ue(["expand-icon",{expanded:Te.value.basic}])},"▼",2)])]),B(p("div",up,[p("label",pp,[B(p("input",{type:"checkbox","onUpdate:modelValue":E[1]||(E[1]=C=>l.value=C)},null,512),[[kt,l.value]]),X(" "+A(F(n)("stamp.basic.extractCircle")),1)]),p("label",null,[X(A(F(n)("stamp.basic.width"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":E[2]||(E[2]=C=>Ws.value=C),min:"1",max:"50",step:"1"},null,512),[[te,Ws.value,void 0,{number:!0}]])]),p("label",null,[X(A(F(n)("stamp.basic.height"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":E[3]||(E[3]=C=>$s.value=C),min:"1",max:"50",step:"1"},null,512),[[te,$s.value,void 0,{number:!0}]])]),p("label",null,[X(A(F(n)("stamp.basic.borderWidth"))+": ",1),B(p("input",{type:"number",step:"0.1","onUpdate:modelValue":E[4]||(E[4]=C=>y.value=C)},null,512),[[te,y.value,void 0,{number:!0}]])]),p("label",null,[X(A(F(n)("stamp.basic.color"))+": ",1),B(p("input",{type:"color","onUpdate:modelValue":E[5]||(E[5]=C=>w.value=C)},null,512),[[te,w.value]])]),p("label",dp,[B(p("input",{type:"checkbox","onUpdate:modelValue":E[6]||(E[6]=C=>_s.value=C)},null,512),[[kt,_s.value]]),X(" "+A(F(n)("stamp.outBorder.enable")),1)]),_s.value?(de(),me("label",gp,[X(A(F(n)("stamp.outBorder.lineWidth"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":E[7]||(E[7]=C=>Us.value=C),min:"0.1",max:"5",step:"0.1"},null,512),[[te,Us.value,void 0,{number:!0}]])])):ct("",!0)],512),[[Ct,Te.value.basic]])]),p("div",mp,[p("div",{class:"group-header",onClick:E[8]||(E[8]=C=>_t("company"))},[p("h3",null,[X(A(F(n)("stamp.company.title")),1),p("span",{class:Ue(["expand-icon",{expanded:Te.value.company}])},"▼",2)])]),B(p("div",hp,[(de(!0),me(Fe,null,Ft(De.value,(C,le)=>(de(),me("div",{key:le,class:"company-item"},[p("div",yp,[p("span",null,A(F(n)("stamp.common.line",{index:le+1})),1),p("button",{class:"small-button delete-button",onClick:Y=>dr(le)},A(F(n)("stamp.common.delete")),9,vp)]),p("label",null,[X(A(F(n)("stamp.company.name"))+": ",1),B(p("input",{type:"text","onUpdate:modelValue":Y=>C.companyName=Y},null,8,_p),[[te,C.companyName]])]),p("label",null,[X(A(F(n)("stamp.company.font"))+": ",1),p("div",bp,[B(p("select",{"onUpdate:modelValue":Y=>C.fontFamily=Y,class:"font-select",onChange:os},[(de(!0),me(Fe,null,Ft(Hs.value,Y=>(de(),me("option",{key:Y,value:Y,style:Ls({fontFamily:Y})},A(Y),13,Sp))),128))],40,Cp),[[Gt,C.fontFamily]]),B(p("input",{type:"text","onUpdate:modelValue":Y=>C.fontFamily=Y,class:"font-input",onInput:os,placeholder:F(n)("stamp.common.fontPlaceholder")},null,40,Tp),[[te,C.fontFamily]])])]),p("label",null,[X(A(F(n)("stamp.company.fontSize"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":Y=>C.fontHeight=Y,min:"1",max:"10",step:"0.1"},null,8,wp),[[te,C.fontHeight,void 0,{number:!0}]])]),p("label",null,[X(A(F(n)("stamp.company.fontWeight"))+": ",1),B(p("select",{"onUpdate:modelValue":Y=>C.fontWeight=Y},[p("option",zp,A(F(n)("stamp.common.fontWeight.normal")),1),p("option",Pp,A(F(n)("stamp.common.fontWeight.bold")),1),E[51]||(E[51]=zn('<option value="100" data-v-539352cb>100</option><option value="200" data-v-539352cb>200</option><option value="300" data-v-539352cb>300</option><option value="400" data-v-539352cb>400</option><option value="500" data-v-539352cb>500</option><option value="600" data-v-539352cb>600</option><option value="700" data-v-539352cb>700</option><option value="800" data-v-539352cb>800</option><option value="900" data-v-539352cb>900</option>',9))],8,Ep),[[Gt,C.fontWeight]])]),p("label",null,[X(A(F(n)("stamp.company.compression"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":Y=>C.compression=Y,min:"0.5",max:"1.5",step:"0.05"},null,8,Ip),[[te,C.compression,void 0,{number:!0}]]),p("span",null,A(C.compression.toFixed(2)),1)]),p("label",null,[X(A(F(n)("stamp.company.distribution"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":Y=>C.textDistributionFactor=Y,min:"0",max:"50",step:"0.1"},null,8,Lp),[[te,C.textDistributionFactor,void 0,{number:!0}]]),p("span",null,A(C.textDistributionFactor.toFixed(2)),1)]),p("label",null,[X(A(F(n)("stamp.company.margin"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":Y=>C.borderOffset=Y,min:"-10",max:"10",step:"0.05"},null,8,xp),[[te,C.borderOffset,void 0,{number:!0}]])]),p("label",null,[X(A(F(n)("stamp.company.startAngle"))+": ",1),p("div",Mp,[B(p("input",{type:"range","onUpdate:modelValue":Y=>C.startAngle=Y,min:"-3.14",max:"3.14",step:"0.01"},null,8,Ap),[[te,C.startAngle,void 0,{number:!0}]]),p("span",null,A((C.startAngle*180/Math.PI).toFixed(0))+"°",1)])]),p("label",null,[X(A(F(n)("stamp.company.rotateDirection"))+": ",1),B(p("select",{"onUpdate:modelValue":Y=>C.rotateDirection=Y},[p("option",Dp,A(F(n)("stamp.company.clockwise")),1),p("option",Op,A(F(n)("stamp.company.counterclockwise")),1)],8,Np),[[Gt,C.rotateDirection]])])]))),128)),p("button",{class:"add-button",onClick:pr},A(F(n)("stamp.common.addNew")),1)],512),[[Ct,Te.value.company]])]),p("div",Rp,[p("div",{class:"group-header",onClick:E[9]||(E[9]=C=>_t("stampType"))},[p("h3",null,[X(A(F(n)("stamp.stampType.title")),1),p("span",{class:Ue(["expand-icon",{expanded:Te.value.stampType}])},"▼",2)])]),B(p("div",Fp,[(de(!0),me(Fe,null,Ft(Re.value,(C,le)=>(de(),me("div",{key:le,class:"stamp-type-item"},[p("div",kp,[p("span",null,A(F(n)("stamp.stampType.line",{index:le+1})),1),p("button",{class:"small-button delete-button",onClick:Y=>ur(le)},A(F(n)("stamp.stampType.delete")),9,Up)]),p("label",null,[X(A(F(n)("stamp.stampType.type"))+": ",1),B(p("input",{type:"text","onUpdate:modelValue":Y=>C.stampType=Y},null,8,Wp),[[te,C.stampType]])]),p("label",null,[X(A(F(n)("stamp.stampType.fontSize"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":Y=>C.fontHeight=Y,min:"1",max:"10",step:"0.1"},null,8,$p),[[te,C.fontHeight,void 0,{number:!0}]])]),p("label",null,[X(A(F(n)("stamp.stampType.font"))+": ",1),p("div",Vp,[B(p("input",{type:"text","onUpdate:modelValue":Y=>C.fontFamily=Y,list:"stampTypeFontList",class:"font-input"},null,8,Hp),[[te,C.fontFamily]]),p("datalist",jp,[(de(!0),me(Fe,null,Ft(Hs.value,Y=>(de(),me("option",{key:Y,value:Y},A(Y),9,Yp))),128))])])]),p("label",null,[X(A(F(n)("stamp.stampType.fontWeight"))+": ",1),B(p("select",{"onUpdate:modelValue":Y=>C.fontWeight=Y},E[52]||(E[52]=[zn('<option value="normal" data-v-539352cb>正常</option><option value="bold" data-v-539352cb>粗体</option><option value="100" data-v-539352cb>100</option><option value="200" data-v-539352cb>200</option><option value="300" data-v-539352cb>300</option><option value="400" data-v-539352cb>400</option><option value="500" data-v-539352cb>500</option><option value="600" data-v-539352cb>600</option><option value="700" data-v-539352cb>700</option><option value="800" data-v-539352cb>800</option><option value="900" data-v-539352cb>900</option>',11)]),8,Bp),[[Gt,C.fontWeight]])]),p("label",null,[X(A(F(n)("stamp.stampType.compression"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":Y=>C.compression=Y,min:"0.1",max:"1.5",step:"0.05"},null,8,Xp),[[te,C.compression,void 0,{number:!0}]]),p("span",null,A(C.compression.toFixed(2)),1)]),p("label",null,[X(A(F(n)("stamp.stampType.letterSpacing"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":Y=>C.letterSpacing=Y,min:"-1",max:"10",step:"0.05"},null,8,Gp),[[te,C.letterSpacing,void 0,{number:!0}]]),p("span",null,A(C.letterSpacing.toFixed(2)),1)]),p("label",null,[X(A(F(n)("stamp.stampType.verticalPosition"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":Y=>C.positionY=Y,min:"-20",max:"20",step:"0.5"},null,8,Kp),[[te,C.positionY,void 0,{number:!0}]])])]))),128)),p("button",{class:"add-button",onClick:fr},A(F(n)("stamp.stampType.addNew")),1)],512),[[Ct,Te.value.stampType]])]),p("div",Jp,[p("div",{class:"group-header",onClick:E[10]||(E[10]=C=>_t("code"))},[p("h3",null,[X(A(F(n)("stamp.code.title")),1),p("span",{class:Ue(["expand-icon",{expanded:Te.value.code}])},"▼",2)])]),B(p("div",Qp,[p("label",null,[X(A(F(n)("stamp.code.code"))+": ",1),B(p("input",{"onUpdate:modelValue":E[11]||(E[11]=C=>u.value=C)},null,512),[[te,u.value]])]),p("label",null,[X(A(F(n)("stamp.code.font"))+": ",1),p("div",qp,[B(p("select",{"onUpdate:modelValue":E[12]||(E[12]=C=>T.value=C),class:"font-select",onChange:os},[(de(!0),me(Fe,null,Ft(Hs.value,C=>(de(),me("option",{key:C,value:C,style:Ls({fontFamily:C})},A(C),13,Zp))),128))],544),[[Gt,T.value]]),B(p("input",{type:"text","onUpdate:modelValue":E[13]||(E[13]=C=>T.value=C),class:"font-input",onInput:os,placeholder:"输入字体名称"},null,544),[[te,T.value]])])]),p("label",null,[X(A(F(n)("stamp.code.fontSize"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":E[14]||(E[14]=C=>L.value=C),step:"0.1"},null,512),[[te,L.value,void 0,{number:!0}]])]),p("label",null,[X(A(F(n)("stamp.code.fontWeight"))+": ",1),B(p("select",{"onUpdate:modelValue":E[15]||(E[15]=C=>G.value=C)},[p("option",ed,A(F(n)("stamp.common.fontWeight.normal")),1),p("option",td,A(F(n)("stamp.common.fontWeight.bold")),1),E[53]||(E[53]=zn('<option value="100" data-v-539352cb>100</option><option value="200" data-v-539352cb>200</option><option value="300" data-v-539352cb>300</option><option value="400" data-v-539352cb>400</option><option value="500" data-v-539352cb>500</option><option value="600" data-v-539352cb>600</option><option value="700" data-v-539352cb>700</option><option value="800" data-v-539352cb>800</option><option value="900" data-v-539352cb>900</option>',9))],512),[[Gt,G.value]])]),p("label",null,[p("span",null,A(F(n)("stamp.common.compression",{value:ne.value.toFixed(2)})),1),B(p("input",{type:"range","onUpdate:modelValue":E[16]||(E[16]=C=>ne.value=C),min:"0.0",max:"3",step:"0.01"},null,512),[[te,ne.value,void 0,{number:!0}]])]),p("label",null,[p("span",null,A(F(n)("stamp.common.distribution",{value:ce.value.toFixed(1)})),1),B(p("input",{type:"range","onUpdate:modelValue":E[17]||(E[17]=C=>ce.value=C),min:"0",max:"100",step:"0.5"},null,512),[[te,ce.value,void 0,{number:!0}]])]),p("label",null,[X(A(F(n)("stamp.code.margin"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":E[18]||(E[18]=C=>_e.value=C),min:"-10",max:"20",step:"0.05"},null,512),[[te,_e.value,void 0,{number:!0}]])])],512),[[Ct,Te.value.code]])]),p("div",sd,[p("div",{class:"group-header",onClick:E[19]||(E[19]=C=>_t("taxNumber"))},[p("h3",null,[X(A(F(n)("stamp.taxNumber.title")),1),p("span",{class:Ue(["expand-icon",{expanded:Te.value.taxNumber}])},"▼",2)])]),B(p("div",nd,[p("label",null,[X(A(F(n)("stamp.taxNumber.number"))+": ",1),B(p("input",{"onUpdate:modelValue":E[20]||(E[20]=C=>f.value=C)},null,512),[[te,f.value]])]),p("label",null,[X(A(F(n)("stamp.taxNumber.font"))+": ",1),p("div",id,[B(p("select",{"onUpdate:modelValue":E[21]||(E[21]=C=>ee.value=C),class:"font-select",onChange:os},[(de(!0),me(Fe,null,Ft(Hs.value,C=>(de(),me("option",{key:C,value:C,style:Ls({fontFamily:C})},A(C),13,od))),128))],544),[[Gt,ee.value]]),B(p("input",{type:"text","onUpdate:modelValue":E[22]||(E[22]=C=>ee.value=C),class:"font-input",onInput:os,placeholder:"输入字体名称"},null,544),[[te,ee.value]])])]),p("label",null,[X(A(F(n)("stamp.taxNumber.fontWeight"))+": ",1),B(p("select",{"onUpdate:modelValue":E[23]||(E[23]=C=>Ie.value=C)},E[54]||(E[54]=[zn('<option value="normal" data-v-539352cb>正常</option><option value="bold" data-v-539352cb>粗体</option><option value="100" data-v-539352cb>100</option><option value="200" data-v-539352cb>200</option><option value="300" data-v-539352cb>300</option><option value="400" data-v-539352cb>400</option><option value="500" data-v-539352cb>500</option><option value="600" data-v-539352cb>600</option><option value="700" data-v-539352cb>700</option><option value="800" data-v-539352cb>800</option><option value="900" data-v-539352cb>900</option>',11)]),512),[[Gt,Ie.value]])]),p("label",null,[p("span",null,A(F(n)("stamp.common.compression",{value:P.value.toFixed(2)})),1),B(p("input",{type:"range","onUpdate:modelValue":E[24]||(E[24]=C=>P.value=C),min:"0.0",max:"3",step:"0.01"},null,512),[[te,P.value,void 0,{number:!0}]])]),p("label",null,[p("span",null,A(F(n)("stamp.common.letterSpacing",{value:W.value.toFixed(2)})),1),B(p("input",{type:"range","onUpdate:modelValue":E[25]||(E[25]=C=>W.value=C),min:"-1",max:"20",step:"0.05"},null,512),[[te,W.value,void 0,{number:!0}]])]),p("label",null,[p("span",null,A(F(n)("stamp.common.verticalPosition",{value:k.value.toFixed(1)})),1),B(p("input",{type:"range","onUpdate:modelValue":E[26]||(E[26]=C=>k.value=C),min:"-10",max:"10",step:"0.1"},null,512),[[te,k.value,void 0,{number:!0}]])])],512),[[Ct,Te.value.taxNumber]])]),p("div",ad,[p("div",{class:"group-header",onClick:E[27]||(E[27]=C=>_t("images"))},[p("h3",null,[X(A(F(n)("stamp.images.title")),1),p("span",{class:Ue(["expand-icon",{expanded:Te.value.images}])},"▼",2)])]),B(p("div",ld,[(de(!0),me(Fe,null,Ft(mt.value,(C,le)=>(de(),me("div",{key:le,class:"image-item"},[p("div",rd,[p("span",null,A(F(n)("stamp.images.image",{index:le+1})),1),p("button",{class:"small-button delete-button",onClick:Y=>rr(le)},"删除",8,cd)]),C.imageUrl?(de(),me("div",fd,[p("img",{src:C.imageUrl,alt:F(n)("stamp.common.preview")},null,8,ud)])):ct("",!0),p("label",null,[X(A(F(n)("stamp.images.select"))+": ",1),p("input",{type:"file",onChange:Y=>cr(Y,le),accept:"image/*"},null,40,pd)]),p("label",null,[X(A(F(n)("stamp.images.width"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":Y=>C.imageWidth=Y,min:"1",max:"100",step:"0.5"},null,8,dd),[[te,C.imageWidth,void 0,{number:!0}]])]),p("label",null,[X(A(F(n)("stamp.images.height"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":Y=>C.imageHeight=Y,min:"1",max:"100",step:"0.5"},null,8,gd),[[te,C.imageHeight,void 0,{number:!0}]])]),p("label",null,[X(A(F(n)("stamp.images.positionX"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":Y=>C.positionX=Y,min:"-20",max:"20",step:"0.5"},null,8,md),[[te,C.positionX,void 0,{number:!0}]])]),p("label",null,[X(A(F(n)("stamp.images.positionY"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":Y=>C.positionY=Y,min:"-20",max:"20",step:"0.5"},null,8,hd),[[te,C.positionY,void 0,{number:!0}]])]),p("label",yd,[B(p("input",{type:"checkbox","onUpdate:modelValue":Y=>C.keepAspectRatio=Y},null,8,vd),[[kt,C.keepAspectRatio]]),X(" "+A(F(n)("stamp.images.keepRatio")),1)])]))),128)),p("button",{class:"add-button",onClick:lr},A(F(n)("stamp.common.addNew")),1)],512),[[Ct,Te.value.images]])]),p("div",_d,[p("div",{class:"group-header",onClick:E[28]||(E[28]=C=>_t("star"))},[p("h3",null,[X(A(F(n)("stamp.star.title")),1),p("span",{class:Ue(["expand-icon",{expanded:Te.value.star}])},"▼",2)])]),B(p("div",bd,[p("label",Cd,[B(p("input",{type:"checkbox","onUpdate:modelValue":E[29]||(E[29]=C=>v.value=C)},null,512),[[kt,v.value]]),X(" "+A(F(n)("stamp.star.enable")),1)]),v.value?(de(),me("div",Sd,[p("label",null,[X(A(F(n)("stamp.star.diameter"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":E[30]||(E[30]=C=>x.value=C),step:"0.1"},null,512),[[te,x.value,void 0,{number:!0}]])]),p("label",null,[X(A(F(n)("stamp.star.verticalPosition"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":E[31]||(E[31]=C=>re.value=C),min:"-10",max:"10",step:"0.1"},null,512),[[te,re.value,void 0,{number:!0}]])])])):ct("",!0)],512),[[Ct,Te.value.star]])]),p("div",Td,[p("div",{class:"group-header",onClick:E[32]||(E[32]=C=>_t("security"))},[p("h3",null,[X(A(F(n)("stamp.security.title")),1),p("span",{class:Ue(["expand-icon",{expanded:Te.value.security}])},"▼",2)])]),B(p("div",wd,[p("label",null,[X(A(F(n)("stamp.security.enable"))+": ",1),B(p("input",{type:"checkbox","onUpdate:modelValue":E[33]||(E[33]=C=>Oe.value=C)},null,512),[[kt,Oe.value]])]),p("button",{onClick:E[34]||(E[34]=C=>Vs(!0,!1))},A(F(n)("stamp.security.refresh")),1),p("label",null,[X(A(F(n)("stamp.security.count"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":E[35]||(E[35]=C=>gt.value=C),min:"1",max:"100",step:"1"},null,512),[[te,gt.value,void 0,{number:!0}]])]),p("label",null,[X(A(F(n)("stamp.security.length"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":E[36]||(E[36]=C=>m.value=C),min:"0.1",max:"100",step:"0.1"},null,512),[[te,m.value,void 0,{number:!0}]])]),p("label",null,[X(A(F(n)("stamp.security.width"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":E[37]||(E[37]=C=>tt.value=C),min:"0.05",max:"0.5",step:"0.05"},null,512),[[te,tt.value,void 0,{number:!0}]])])],512),[[Ct,Te.value.security]])]),p("div",Ed,[p("div",{class:"group-header",onClick:E[38]||(E[38]=C=>_t("roughEdge"))},[p("h3",null,[X(A(F(n)("stamp.roughEdge.title")),1),p("span",{class:Ue(["expand-icon",{expanded:Te.value.roughEdge}])},"▼",2)])]),B(p("div",zd,[p("label",Pd,[B(p("input",{type:"checkbox","onUpdate:modelValue":E[39]||(E[39]=C=>j.value=C)},null,512),[[kt,j.value]]),X(" "+A(F(n)("stamp.roughEdge.enable")),1)]),j.value?(de(),me("label",Id,[X(A(F(n)("stamp.roughEdge.width"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":E[40]||(E[40]=C=>Q.value=C),min:"0.05",max:"0.5",step:"0.05"},null,512),[[te,Q.value,void 0,{number:!0}]]),p("span",null,A(Q.value.toFixed(2)),1)])):ct("",!0),j.value?(de(),me("label",Ld,[X(A(F(n)("stamp.roughEdge.height"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":E[41]||(E[41]=C=>oe.value=C),min:"0.1",max:"5",step:"0.1"},null,512),[[te,oe.value,void 0,{number:!0}]]),p("span",null,A(oe.value.toFixed(1)),1)])):ct("",!0),j.value?(de(),me("label",xd,[X(A(F(n)("stamp.roughEdge.probability"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":E[42]||(E[42]=C=>ae.value=C),min:"0",max:"1",step:"0.01"},null,512),[[te,ae.value,void 0,{number:!0}]]),p("span",null,A(ae.value.toFixed(2)),1)])):ct("",!0),j.value?(de(),me("label",Md,[X(A(F(n)("stamp.roughEdge.shift"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":E[43]||(E[43]=C=>Ee.value=C),min:"-10",max:"10",step:"0.01"},null,512),[[te,Ee.value,void 0,{number:!0}]]),p("span",null,A(Ee.value.toFixed(2)),1)])):ct("",!0),j.value?(de(),me("label",Ad,[X(A(F(n)("stamp.roughEdge.points"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":E[44]||(E[44]=C=>Ae.value=C),min:"100",max:"1000",step:"10"},null,512),[[te,Ae.value,void 0,{number:!0}]]),p("span",null,A(Ae.value),1)])):ct("",!0),p("button",{onClick:E[45]||(E[45]=C=>Vs(!1,!1,!0))},A(F(n)("stamp.roughEdge.refresh")),1)],512),[[Ct,Te.value.roughEdge]])]),p("div",Nd,[p("div",{class:"group-header",onClick:E[46]||(E[46]=C=>_t("aging"))},[p("h3",null,[X(A(F(n)("stamp.aging.title")),1),p("span",{class:Ue(["expand-icon",{expanded:Te.value.aging}])},"▼",2)])]),B(p("div",Dd,[p("label",Od,[B(p("input",{type:"checkbox","onUpdate:modelValue":E[47]||(E[47]=C=>b.value=C),onChange:ai},null,544),[[kt,b.value]]),X(" "+A(F(n)("stamp.aging.enable")),1)]),p("label",Rd,[B(p("input",{type:"checkbox","onUpdate:modelValue":E[48]||(E[48]=C=>D.value=C),onChange:ai},null,544),[[kt,D.value]]),X(" "+A(F(n)("stamp.aging.manual")),1)]),b.value?(de(),me("label",Fd,[X(A(F(n)("stamp.aging.intensity"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":E[49]||(E[49]=C=>$.value=C),min:"0",max:"100",step:"1",onInput:ai},null,544),[[te,$.value,void 0,{number:!0}]]),p("span",null,A($.value)+"%",1)])):ct("",!0),p("button",{onClick:br,class:"refresh-button"},A(F(n)("stamp.aging.refresh")),1)],512),[[Ct,Te.value.aging]])]),p("div",kd,[p("div",{class:"group-header",onClick:E[50]||(E[50]=C=>_t("innerCircle"))},[p("h3",null,[X(A(F(n)("stamp.innerCircle.title")),1),p("span",{class:Ue(["expand-icon",{expanded:Te.value.innerCircle}])},"▼",2)])]),B(p("div",Ud,[p("button",{onClick:gr},A(F(n)("stamp.innerCircle.addNew")),1),(de(!0),me(Fe,null,Ft(Nt.value,(C,le)=>(de(),me("div",{key:le,class:"inner-circle-item"},[p("div",Wd,[p("span",null,"第 "+A(le+1)+" 行",1),p("button",{class:"small-button delete-button",onClick:Y=>mr(le)},"删除",8,$d)]),p("label",null,[X(A(F(n)("stamp.innerCircle.lineWidth"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":Y=>C.innerCircleLineWidth=Y,min:"0.05",max:"0.5",step:"0.05"},null,8,Vd),[[te,C.innerCircleLineWidth,void 0,{number:!0}]])]),p("label",null,[X(A(F(n)("stamp.innerCircle.radiusX"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":Y=>C.innerCircleLineRadiusX=Y,min:"1",max:"50",step:"0.1"},null,8,Hd),[[te,C.innerCircleLineRadiusX,void 0,{number:!0}]])]),p("label",null,[X(A(F(n)("stamp.innerCircle.radiusY"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":Y=>C.innerCircleLineRadiusY=Y,min:"1",max:"50",step:"0.1"},null,8,jd),[[te,C.innerCircleLineRadiusY,void 0,{number:!0}]])])]))),128))],512),[[Ct,Te.value.innerCircle]])])],512)],2))}}),ni=(e,t)=>{const s=e.__vccOpts||e;for(const[n,i]of t)s[n]=i;return s},Bd=ni(Yd,[["__scopeId","data-v-539352cb"]]),Xd={key:0,class:"template-dialog-overlay"},Gd={class:"template-dialog"},Kd={class:"template-dialog-header"},Jd={class:"template-dialog-content"},Qd={class:"template-list"},qd={class:"template-category"},Zd={class:"template-grid"},eg=["onClick"],tg={class:"template-preview"},sg=["src","alt"],ng={class:"template-info"},ig={class:"template-name"},og={class:"template-dialog-footer"},ag={class:"footer-buttons"},lg=hs({__name:"TemplateDialog",props:{show:{type:Boolean},templates:{},currentIndex:{},drawStampUtils:{}},emits:["close","save","select","update"],setup(e,{emit:t}){const{t:s}=ys(),n=V(null),i=e,o=t,a=()=>{o("close")},l=d=>{o("select",d)},r=()=>{if(!i.drawStampUtils)return;const d=i.drawStampUtils.getDrawConfigs(),h=JSON.stringify(d,null,2),T=new Blob([h],{type:"application/json"}),L=URL.createObjectURL(T),M=document.createElement("a");M.href=L,M.download="stamp_template.json",document.body.appendChild(M),M.click(),document.body.removeChild(M),URL.revokeObjectURL(L)},u=()=>{var d;(d=n.value)==null||d.click()},f=async d=>{var T;const h=d.target;if((T=h.files)!=null&&T.length){try{const M=await h.files[0].text(),U=JSON.parse(M);i.drawStampUtils&&(i.drawStampUtils.setDrawConfigs(U),o("update"))}catch(L){console.error("加载模板失败:",L),alert(s("stamp.template.loadError"))}h.value=""}};return(d,h)=>d.show?(de(),me("div",Xd,[p("div",Gd,[p("div",Kd,[p("h3",null,A(F(s)("stamp.template.title")),1),p("button",{class:"close-button",onClick:a},"×")]),p("div",Jd,[p("div",Qd,[p("div",qd,[p("h4",null,A(F(s)("stamp.template.defaultTitle")),1),p("div",Zd,[(de(!0),me(Fe,null,Ft(d.templates,(T,L)=>(de(),me("div",{key:"default-"+L,class:Ue(["template-item",{active:d.currentIndex===-1-L}]),onClick:M=>l(T)},[p("div",tg,[p("img",{src:T.preview,alt:F(s)("stamp.template.preview")},null,8,sg)]),p("div",ng,[p("span",ig,A(T.name),1)])],10,eg))),128))])])])]),p("div",og,[p("input",{type:"file",ref_key:"templateFileInput",ref:n,style:{display:"none"},accept:".json",onChange:f},null,544),p("div",ag,[p("button",{class:"load-template",onClick:u},A(F(s)("stamp.template.load")),1),p("button",{class:"add-template",onClick:r},A(F(s)("stamp.template.save")),1)])])])])):ct("",!0)}}),rg=ni(lg,[["__scopeId","data-v-efd8fb1b"]]),cg={key:0,class:"legal-dialog-overlay"},fg={class:"legal-dialog"},ug={class:"legal-content"},pg={style:{"white-space":"pre-line"}},dg={class:"dialog-buttons"},gg={class:"disclaimer-content"},mg={class:"warning-text"},hg={style:{"white-space":"pre-line"}},yg={class:"canvas-container"},vg={class:"canvas-wrapper"},_g={class:"bottom-toolbar"},bg={class:"drag-switch-container"},Cg={class:"drag-label"},Sg={class:"switch"},Da=10,Tg=hs({__name:"DrawStampUtilsDemo",setup(e){const{t}=ys(),s=V(!1),n=V(null),i=V(null),o=V(!1),a=V(!1);let l;const r=()=>{l=new bi(i.value,Da)};let u=[],f=[],d=[],h=[],T=[];const L=(q=!1,G=!1,ee=!1)=>{l.refreshStamp(q,G,ee),l.setDraggable(a.value),f=l.drawCompanyUtils.getTextPaths(),d=l.drawCodeUtils.getTextPaths(),h=l.drawStampTypeUtils.getTextPaths(),T=l.drawTaxNumberUtils.getTextPaths(),u=[...f,...d,...h,...T]},M=()=>{o.value=!0},U=()=>{o.value=!1},y=()=>{o.value=!1,l.saveStampAsPNG()},w=V([]),x=async()=>{w.value=await or()};Kn(async()=>{var q;r(),await x(),l.setDraggable(a.value),i.value&&(i.value.style.cursor=a.value?"move":"default"),L(),dt(),document.querySelectorAll(".font-select, .font-input").forEach(G=>{G instanceof HTMLElement&&R({target:G})}),s.value=!0,window.addEventListener("mousemove",re),(q=l==null?void 0:l.canvas)==null||q.addEventListener("click",Me)}),Jn(()=>{var q;window.removeEventListener("mousemove",re),(q=l==null?void 0:l.canvas)==null||q.removeEventListener("click",Me)});const b=V([{id:"contract",name:"合同专用章",text:"合同专用章",fontSize:4.6,letterSpacing:0,lineSpacing:1.2,positionY:-5,compression:1},{id:"invoice",name:"印章类型",text:`发票专章
增值税专用`,fontSize:4.2,letterSpacing:0,lineSpacing:1.5,positionY:-4,compression:.9},{id:"finance",name:"财务专用章",text:`财务专用章
仅限报销使用`,fontSize:4,letterSpacing:0,lineSpacing:1.8,positionY:-3,compression:.85}]),D=()=>{localStorage.setItem("stampTypePresets",JSON.stringify(b.value))};vt(b,()=>{D()},{deep:!0});const $=()=>{window.open("https://xxss0903.github.io/extractstamp/","_blank")},R=q=>{var Ie,pe;const G=q.target,ee=(G.tagName==="SELECT",G.value);if(G.style.setProperty("--current-font",ee),G.tagName==="SELECT"){const ne=(Ie=G.parentElement)==null?void 0:Ie.querySelector(".font-input");ne&&(ne.value=ee,ne.style.setProperty("--current-font",ee))}if(G.tagName==="INPUT"){const ne=(pe=G.parentElement)==null?void 0:pe.querySelector(".font-select");ne&&(ne.value=ee,ne.style.setProperty("--current-font",ee))}};V({basic:!1,company:!1,stampType:!1,code:!1,taxNumber:!1,star:!1,security:!1,roughEdge:!1,aging:!1,innerCircle:!1,images:!1});const se=V(-1),fe=async()=>{localStorage.setItem("stampTemplates",JSON.stringify(templateList.value))},ve=q=>{var G;try{s.value=!1,l=new bi(i.value,Da),L(),document.querySelectorAll(".font-select, .font-input").forEach(Ie=>{Ie instanceof HTMLElement&&R({target:Ie})}),s.value=!0,window.addEventListener("mousemove",re),(G=l==null?void 0:l.canvas)==null||G.addEventListener("click",Me);const ee=JSON.parse(JSON.stringify(q.config));ee.ruler.showRuler=!0,ee.ruler.showFullRuler=!0,ee.ruler.showSideRuler=!0,ee.ruler.showCrossLine=!0,ee.ruler.showCurrentPositionText=!0,ee.ruler.showDashLine=!0,ee.company.startAngle=q.config.company.startAngle,ee.company.rotateDirection=q.config.company.rotateDirection,l.setDrawConfigs(ee),L(),setTimeout(()=>{s.value=!0,console.log("refresh editor controls"),n.value.restoreDrawConfigs()},100),se.value=-1-_e.findIndex(Ie=>Ie===q)}catch(ee){console.error("加载默认模板失败:",ee),alert("加载默认模板失败")}},_e=[{name:"印章1",preview:"",config:O1},{name:"印章2",preview:"",config:cp}],ce=V(localStorage.getItem("showSecurityWarning")!=="false");vt(ce,q=>{localStorage.setItem("showSecurityWarning",String(q))});const je=(q,G,ee,Ie)=>{l.setDrawConfigs(q),l.refreshStamp(G,ee,Ie)},Ze=V(!1),dt=()=>{_e.forEach(async q=>{const G=document.createElement("canvas");G.width=500,G.height=500;const ee=new bi(G,8);q.config.ruler.showRuler=!1,ee.setDrawConfigs(q.config),ee.refreshStamp(),q.preview=G.toDataURL("image/png")})},xe=V(!1),ge=V({left:"0px",top:"0px"}),re=q=>{if(!(l!=null&&l.canvas))return;const G=l.canvas.getBoundingClientRect(),ee=q.clientX-G.left,Ie=q.clientY-G.top;let pe=!1;for(const ne of u)if(ee>=ne.bounds.x&&ee<=ne.bounds.x+ne.bounds.width&&Ie>=ne.bounds.y&&Ie<=ne.bounds.y+ne.bounds.height){pe=!0,xe.value=!0,ge.value={left:`${q.clientX+10}px`,top:`${q.clientY+10}px`},l.canvas.style.cursor="pointer";return}pe||(xe.value=!1,l.canvas.style.cursor="default")},Me=q=>{if(!(l!=null&&l.canvas))return;const G=l.canvas.getBoundingClientRect(),ee=q.clientX-G.left,Ie=q.clientY-G.top;for(const pe of u)if(ee>=pe.bounds.x&&ee<=pe.bounds.x+pe.bounds.width&&Ie>=pe.bounds.y&&Ie<=pe.bounds.y+pe.bounds.height){if(console.log("点击的文字:",pe.text),console.log("文字路径:",pe.path),console.log("文字边界:",pe.bounds),console.log("文字类型:",pe.type),pe.type==="company"){const ne=et(pe.text);if(ne!==-1){const Oe=Oe.value;Oe&&Oe.scrollToCompanyText(ne)}}else if(pe.type==="code"){const ne=ne.value;ne&&ne.scrollToCode()}else if(pe.type==="stampType"){const ne=Ye(pe.text);if(ne!==-1){const Oe=Oe.value;Oe&&Oe.scrollToStampType(ne)}}else if(pe.type==="taxNumber"){const ne=ne.value;ne&&ne.scrollToTaxNumber()}return}},et=q=>l.getDrawConfigs().companyList.findIndex(G=>G.companyName.includes(q)),Ye=q=>l.getDrawConfigs().stampTypeList.findIndex(G=>G.stampType.includes(q));return vt(a,q=>{l&&(l.setDraggable(q),i.value&&(i.value.style.cursor=q?"move":"default"))}),(q,G)=>(de(),me(Fe,null,[o.value?(de(),me("div",cg,[p("div",fg,[p("h3",null,"⚠️ "+A(F(t)("legal.title")),1),p("div",ug,[p("p",null,[p("strong",null,A(F(t)("legal.warning")),1)]),p("p",null,[p("span",pg,A(F(t)("legal.securityItems")),1)])]),p("div",dg,[p("button",{onClick:U,class:"cancel-button"},A(F(t)("legal.cancel")),1),p("button",{onClick:y,class:"confirm-button"},A(F(t)("legal.confirm")),1)])])])):ct("",!0),p("div",{class:Ue(["container",{"has-warning":ce.value}])},[ce.value?(de(),me("div",{key:0,class:Ue(["legal-disclaimer",{hidden:!ce.value}])},[p("div",gg,[G[4]||(G[4]=p("div",{class:"warning-icon"},"⚠️",-1)),p("div",mg,[p("h3",null,A(F(t)("legal.securityWarning")),1),p("p",null,[p("strong",null,A(F(t)("legal.securityNotice")),1)]),p("p",null,[p("span",hg,A(F(t)("legal.securityItems")),1)]),p("button",{class:"close-warning",onClick:G[0]||(G[0]=ee=>ce.value=!1)},"×")])])],2)):ct("",!0),s.value?(de(),Dl(Bd,{key:1,ref_key:"editorControlsRef",ref:n,drawStampUtils:F(l),onUpdateDrawStamp:je},null,8,["drawStampUtils"])):ct("",!0),p("div",yg,[p("div",vg,[p("canvas",{ref_key:"stampCanvas",ref:i,width:"600",height:"600"},null,512)]),p("div",_g,[p("button",{class:"toolbar-button",onClick:G[1]||(G[1]=ee=>Ze.value=!0)},[G[5]||(G[5]=p("span",{class:"button-icon"},"📋",-1)),X(" "+A(F(t)("stamp.template.open")),1)]),p("button",{class:"toolbar-button",onClick:M},[G[6]||(G[6]=p("span",{class:"button-icon"},"💾",-1)),X(" "+A(F(t)("stamp.save")),1)]),p("button",{class:"toolbar-button",onClick:$},[G[7]||(G[7]=p("span",{class:"button-icon"},"🔍",-1)),X(" "+A(F(t)("stamp.extract.tool")),1)]),p("div",bg,[p("span",Cg,A(F(t)("stamp.drag.label")),1),p("label",Sg,[B(p("input",{type:"checkbox","onUpdate:modelValue":G[2]||(G[2]=ee=>a.value=ee)},null,512),[[kt,a.value]]),G[8]||(G[8]=p("span",{class:"slider round"},null,-1))])])])]),G[9]||(G[9]=p("div",{class:"right-toolbar"},null,-1)),Xe(rg,{show:Ze.value,templates:_e,currentIndex:se.value,drawStampUtils:F(l),onClose:G[3]||(G[3]=ee=>Ze.value=!1),onSave:fe,onSelect:ve},null,8,["show","currentIndex","drawStampUtils"])],2)],64))}}),wg=ni(Tg,[["__scopeId","data-v-d377cffb"]]),Eg={class:"language-switch"},zg=hs({__name:"App",setup(e){const{locale:t}=ys(),s=V(t.value),n=i=>{t.value=i,s.value=i};return(i,o)=>(de(),me(Fe,null,[p("div",Eg,[p("button",{onClick:o[0]||(o[0]=a=>n("zh")),class:Ue({active:s.value==="zh"})},"中文",2),p("button",{onClick:o[1]||(o[1]=a=>n("en")),class:Ue({active:s.value==="en"})},"English",2)]),Xe(wg)],64))}}),Pg=ni(zg,[["__scopeId","data-v-2421fd2e"]]),Ig={zh:{menu:{home:"首页",about:"关于",contact:"联系我们"},legal:{title:"法律提示",warning:"请确认您已知悉并同意以下内容：",securityWarning:"安全警告",securityNotice:"本项目仅供学习和参考！严禁用于任何非法用途！",securityItems:`1. 本项目开源代码仅用于技术学习和交流。
2. 使用本项目生成的任何图片请勿用于任何非法用途。
3. 因违法使用本项目造成的任何法律责任和损失，需自行承担，与本项目无关。
4. 如果使用本项目请遵守相关法律法规。`,cancel:"取消",confirm:"我已知悉并同意"},stamp:{save:"保存印章",saveTemplate:"保存模板",loadTemplate:"加载模板",basic:{title:"印章基本设置",extractCircle:"提取圆形印章",width:"印章宽度 (mm)",height:"印章高度 (mm)",borderWidth:"圆形边框宽度 (mm)",color:"印章颜色"},company:{title:"公司名称列表设置",name:"公司名称",font:"字体",fontSize:"字体大小 (mm)",fontWeight:"字体粗细",compression:"压缩比例",distribution:"分布因子",margin:"边距 (mm)",startAngle:"起始角度 (°)",rotateDirection:"旋转方向",counterclockwise:"逆时针",clockwise:"顺时针"},stampType:{title:"印章类型列表设置",type:"文字内容",fontSize:"字体大小 (mm)",font:"字体",fontWeight:"字体粗细",compression:"压缩比例",letterSpacing:"字符间距 (mm)",verticalPosition:"垂直位置 (mm)",addNew:"添加新行",delete:"删除",line:"第 {index} 行"},star:{title:"五角星设置",enable:"绘制五角星",diameter:"五角星直径 (mm)",verticalPosition:"垂直位置 (mm)"},security:{title:"防伪纹路设置",enable:"启用防伪纹路",refresh:"刷新纹路",count:"纹路数量",length:"纹路长度 (mm)",width:"纹路宽度 (mm)"},roughEdge:{title:"毛边效果设置",enable:"启用毛边效果",width:"毛边宽度 (mm)",height:"毛边高度 (mm)",probability:"毛边概率",shift:"毛边偏移 (mm)",points:"毛边点数",refresh:"刷新毛边"},aging:{title:"做旧效果",enable:"启用做旧效果",manual:"手动做旧",intensity:"做旧强度",refresh:"刷新做旧"},extract:{title:"提取印章",tool:"提取印章工具"},template:{title:"常用模板",save:"保存当前为模板",defaultTitle:"默认模板",preview:"预览",name:"模板名称",open:"打开模板",load:"加载模板"},common:{line:"第 {index} 行",delete:"删除",addNew:"添加新行",fontPlaceholder:"输入字体名称",fontWeightNormal:"正常",fontWeightBold:"粗体",fontWeight:{normal:"正常",bold:"粗体",light:"细体",medium:"中等",semibold:"半粗"},compression:"压缩比例：{value}",distribution:"分布因子：{value}",letterSpacing:"字符间距：{value} mm",verticalPosition:"垂直位置：{value} mm",preview:"预览图片"},code:{title:"印章编码设置",code:"印章编码",font:"字体",fontSize:"字体大小 (mm)",fontWeight:"字体粗细",compression:"压缩比例",distribution:"分布因子",margin:"边距 (mm)"},taxNumber:{title:"中间数字设置",number:"数字",font:"字体",fontSize:"字体大小 (mm)",fontWeight:"字体粗细",compression:"压缩比例",letterSpacing:"字符间距 (mm)",verticalPosition:"垂直位置调整 (mm)"},images:{title:"图片列表设置",image:"图片 {index}",select:"选择图片",width:"图片宽度 (mm)",height:"图片高度 (mm)",positionX:"水平位置 (mm)",positionY:"垂直位置 (mm)",keepRatio:"保持宽高比",preview:"预览"},innerCircle:{addNew:"新增",title:"内圈圆形设置",lineWidth:"内圈圆线宽 (mm)",radiusX:"内圈圆半径X (mm)",radiusY:"内圈圆半径Y (mm)"},outBorder:{enable:"显示外圈边框",lineWidth:"边框宽度 (mm)"},drag:{label:"移动印章"}}},en:{menu:{home:"Home",about:"About",contact:"Contact"},legal:{title:"Legal Notice",warning:"Please confirm that you understand and agree to the following:",securityWarning:"Security Warning",securityNotice:"This project is for learning and reference only! It is strictly prohibited for any illegal use!",securityItems:`1. This project's open source code is only for technical learning and communication.
2. Do not use any images generated by this project for any illegal purposes.
3. You are responsible for any legal liability and losses caused by illegal use of this project.
4. Please comply with relevant laws and regulations when using this project.`,cancel:"Cancel",confirm:"I understand and agree"},stamp:{save:"Save Stamp",saveTemplate:"Save Template",loadTemplate:"Load Template",basic:{title:"Basic Stamp Settings",extractCircle:"Extract Circle Stamp",width:"Stamp Width (mm)",height:"Stamp Height (mm)",borderWidth:"Circle Border Width (mm)",color:"Stamp Color"},company:{title:"Company Name List Settings",name:"Company Name",font:"Font",fontSize:"Font Size (mm)",fontWeight:"Font Weight",compression:"Compression Ratio",distribution:"Distribution Factor",margin:"Margin (mm)",startAngle:"Start Angle (°)",rotateDirection:"Rotate Direction",counterclockwise:"Counter Clockwise",clockwise:"Clockwise"},stampType:{title:"Stamp Type List Settings",type:"Text Content",fontSize:"Font Size (mm)",font:"Font",fontWeight:"Font Weight",compression:"Compression Ratio",letterSpacing:"Letter Spacing (mm)",verticalPosition:"Vertical Position (mm)",addNew:"Add New",delete:"Delete",line:"Line {index}"},star:{title:"Star Settings",enable:"Draw Star",diameter:"Star Diameter (mm)",verticalPosition:"Vertical Position (mm)"},security:{title:"Security Pattern Settings",enable:"Enable Security Pattern",refresh:"Refresh Pattern",count:"Pattern Count",length:"Pattern Length (mm)",width:"Pattern Width (mm)"},roughEdge:{title:"Rough Edge Settings",enable:"Enable Rough Edge",width:"Edge Width (mm)",height:"Edge Height (mm)",probability:"Edge Probability",shift:"Edge Shift (mm)",points:"Edge Points",refresh:"Refresh Edge"},aging:{title:"Aging Effect",enable:"Enable Aging",manual:"Manual Aging",intensity:"Aging Intensity",refresh:"Refresh Aging"},extract:{title:"Extract Stamp",tool:"Extract Stamp Tool"},template:{title:"Common Templates",save:"Save Current as Template",defaultTitle:"Default Templates",preview:"Preview",name:"Template Name",open:"Open Template",load:"Load Template"},common:{line:"Line {index}",delete:"Delete",addNew:"Add New",fontPlaceholder:"Enter font name",fontWeightNormal:"Normal",fontWeightBold:"Bold",fontWeight:{normal:"Normal",bold:"Bold",light:"Light",medium:"Medium",semibold:"Semi Bold"},compression:"Compression: {value}",distribution:"Distribution: {value}",letterSpacing:"Letter Spacing: {value} mm",verticalPosition:"Vertical Position: {value} mm",preview:"Preview Image"},code:{title:"Stamp Code Settings",code:"Stamp Code",font:"Font",fontSize:"Font Size (mm)",fontWeight:"Font Weight",compression:"Compression Ratio",distribution:"Distribution Factor",margin:"Margin (mm)"},taxNumber:{title:"Center Number Settings",number:"Tax Number",font:"Font",fontSize:"Font Size (mm)",fontWeight:"Font Weight",compression:"Compression Ratio",letterSpacing:"Letter Spacing (mm)",verticalPosition:"Vertical Position (mm)"},images:{title:"Image List Settings",image:"Image {index}",select:"Select Image",width:"Image Width (mm)",height:"Image Height (mm)",positionX:"Horizontal Position (mm)",positionY:"Vertical Position (mm)",keepRatio:"Keep Aspect Ratio",preview:"Preview"},innerCircle:{addNew:"Add New",title:"Inner Circle Settings",lineWidth:"Circle Line Width (mm)",radiusX:"Circle Radius X (mm)",radiusY:"Circle Radius Y (mm)"},outBorder:{enable:"Show Outer Border",lineWidth:"Border Width (mm)"},drag:{label:"Move Stamp"}}}},Lg=$0({locale:"zh",fallbackLocale:"en",messages:Ig}),ar=Vf(Pg);ar.use(Lg);ar.mount("#app");
